#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
季节标签分类器 - 最终版本
基于关键词匹配和AI大模型的双重验证机制
"""

import pandas as pd
import re
from typing import Dict, List, Tuple, Optional
import json
import os
from datetime import datetime

class SeasonLabelClassifier:
    """季节标签分类器"""
    
    def __init__(self, config_path: str = "config/rules.json"):
        """初始化分类器"""
        self.config_path = config_path
        self.rules = self._load_rules()
        self.ai_client = None  # AI客户端，需要单独初始化
        
        # 关键词库
        self.ss_keywords = [
            '短袖', '透气', '防晒', '凉鞋', '夏季', '清凉', '薄款', '凉爽', 
            '速干', '冰感', '遮阳', '轻薄', '网眼', '排汗', '凉感', '夏装',
            '泳装', '吊带', '背心', '短裤', '短裙', '洞洞鞋', '沙滩', '凉席',
            '风扇', '冰丝', '春夏', '网面', '降温'
        ]
        
        self.aw_keywords = [
            '羽绒', '保暖', '加厚', '毛呢', '冬季', '厚款', '御寒', '加绒',
            '毛绒', '棉服', '厚实', '保温', '防风', '皮草', '羊毛', '冲锋衣',
            '雪地靴', '秋冬', '抗寒', '棉拖', '靴子', '暖宝宝', '电热毯',
            '暖气', '取暖器', '毛衣', '围巾', '手套', '棉袄', '绒面'
        ]
        
        # 全季类目
        self.all_season_categories = [
            '食品饮料', '母婴用品', '家具家装', '电器数码', '美妆个护',
            '居家日用', '宠物用品', '箱包配饰', '医疗保健', '汽车用品'
        ]
    
    def _load_rules(self) -> Dict:
        """加载分类规则"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def classify_by_keywords(self, product_info: Dict) -> Dict:
        """基于关键词的分类"""
        product_name = str(product_info.get('商品名', '')).lower()
        category = str(product_info.get('类目', '')).lower()
        description = str(product_info.get('商品描述', '')).lower()
        
        # 检查是否为全季类目
        if any(cat in category for cat in self.all_season_categories):
            return {
                'label': '全季',
                'confidence': 0.9,
                'reason': '全季类目'
            }
        
        # 计算关键词匹配分数
        text = f"{product_name} {description}"
        
        ss_score = self._calculate_keyword_score(text, self.ss_keywords)
        aw_score = self._calculate_keyword_score(text, self.aw_keywords)
        
        # 判断结果
        if ss_score > aw_score and ss_score > 0.3:
            return {
                'label': 'SS',
                'confidence': min(ss_score, 0.95),
                'reason': f'SS关键词匹配(分数:{ss_score:.2f})'
            }
        elif aw_score > ss_score and aw_score > 0.3:
            return {
                'label': 'AW',
                'confidence': min(aw_score, 0.95),
                'reason': f'AW关键词匹配(分数:{aw_score:.2f})'
            }
        else:
            return {
                'label': '全季',
                'confidence': 0.7,
                'reason': '无明显季节性特征'
            }
    
    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """计算关键词匹配分数"""
        matches = 0
        for keyword in keywords:
            if keyword in text:
                matches += 1
        
        # 归一化分数
        if len(keywords) > 0:
            base_score = matches / len(keywords)
            # 增强明显关键词的权重
            if matches > 0:
                return min(base_score * 2.5, 1.0)
        
        return 0.0
    
    async def classify_by_ai(self, product_info: Dict) -> Dict:
        """基于AI的分类（需要AI客户端）"""
        if not self.ai_client:
            return {
                'label': '全季',
                'confidence': 0.5,
                'reason': 'AI客户端未初始化'
            }
        
        # 构建提示词
        prompt = self._build_ai_prompt(product_info)
        
        try:
            # 调用AI接口
            response = await self.ai_client.predict(prompt)
            
            # 解析AI响应
            ai_label = self._parse_ai_response(response)
            
            return {
                'label': ai_label,
                'confidence': 0.85,
                'reason': 'AI模型推理'
            }
        except Exception as e:
            return {
                'label': '全季',
                'confidence': 0.5,
                'reason': f'AI调用失败: {str(e)}'
            }
    
    def _build_ai_prompt(self, product_info: Dict) -> str:
        """构建AI提示词"""
        product_name = product_info.get('商品名', '')
        category = product_info.get('类目', '')
        description = product_info.get('商品描述', '')
        
        prompt = f"""
你是一个专业的电商商品季节性分类专家。请根据商品信息判断季节标签，只能输出以下三个标签之一：全季、SS、AW

分类标准：
- 全季：适用于一年四季，无明显季节性特征的商品
- SS：主要在春夏季节使用的商品（短袖、透气、防晒等）
- AW：主要在秋冬季节使用的商品（羽绒、保暖、加厚等）

商品信息：
商品名称：{product_name}
商品类目：{category}
商品描述：{description}

请只返回标签名称（全季、SS、AW），不要添加任何解释。
"""
        return prompt
    
    def _parse_ai_response(self, response: str) -> str:
        """解析AI响应"""
        response = response.strip()
        if 'SS' in response:
            return 'SS'
        elif 'AW' in response:
            return 'AW'
        else:
            return '全季'
    
    def classify_single(self, product_info: Dict) -> Dict:
        """单个商品分类（同步版本）"""
        # 关键词分类
        keyword_result = self.classify_by_keywords(product_info)
        
        # 如果关键词置信度很高，直接返回
        if keyword_result['confidence'] > 0.8:
            return {
                'product_id': product_info.get('商品ID'),
                'keyword_prediction': keyword_result['label'],
                'ai_prediction': keyword_result['label'],  # 使用关键词结果
                'final_label': keyword_result['label'],
                'confidence': keyword_result['confidence'],
                'reasoning': keyword_result['reason'],
                'match_status': '匹配'
            }
        
        # 否则返回关键词结果（在没有AI的情况下）
        return {
            'product_id': product_info.get('商品ID'),
            'keyword_prediction': keyword_result['label'],
            'ai_prediction': keyword_result['label'],
            'final_label': keyword_result['label'],
            'confidence': keyword_result['confidence'],
            'reasoning': keyword_result['reason'],
            'match_status': '匹配'
        }
    
    def classify_batch(self, products: List[Dict]) -> List[Dict]:
        """批量分类"""
        results = []
        for product in products:
            result = self.classify_single(product)
            results.append(result)
        return results
    
    def analyze_mismatch(self, manual_label: str, predicted_label: str, 
                        product_info: Dict) -> Dict:
        """分析不匹配原因"""
        if manual_label == predicted_label:
            return {
                'is_match': True,
                'mismatch_type': None,
                'reason': '标签匹配',
                'possible_error': False
            }
        
        product_name = str(product_info.get('商品名', '')).lower()
        
        # 分析可能的标记错误
        possible_error = False
        error_reason = ""
        
        # 检查明显的季节性特征
        if predicted_label == 'SS':
            if any(kw in product_name for kw in ['短袖', '透气', '防晒', '夏季']):
                possible_error = True
                error_reason = "商品名包含明显SS特征，可能人工标记错误"
        
        elif predicted_label == 'AW':
            if any(kw in product_name for kw in ['羽绒', '保暖', '加厚', '冬季']):
                possible_error = True
                error_reason = "商品名包含明显AW特征，可能人工标记错误"
        
        return {
            'is_match': False,
            'mismatch_type': f"{manual_label}→{predicted_label}",
            'reason': f"人工标记为{manual_label}，AI预测为{predicted_label}",
            'possible_error': possible_error,
            'error_reason': error_reason
        }
    
    def generate_analysis_report(self, results: List[Dict]) -> Dict:
        """生成分析报告"""
        total_count = len(results)
        if total_count == 0:
            return {}
        
        # 统计匹配情况
        matches = sum(1 for r in results if r.get('match_status') == '匹配')
        accuracy = matches / total_count
        
        # 各标签统计
        label_stats = {}
        for label in ['全季', 'SS', 'AW']:
            label_results = [r for r in results if r.get('final_label') == label]
            label_stats[label] = {
                'count': len(label_results),
                'percentage': len(label_results) / total_count * 100
            }
        
        # 置信度统计
        confidences = [r.get('confidence', 0) for r in results if r.get('confidence')]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return {
            'total_count': total_count,
            'accuracy': accuracy,
            'match_count': matches,
            'label_distribution': label_stats,
            'average_confidence': avg_confidence,
            'high_confidence_count': sum(1 for c in confidences if c > 0.8),
            'low_confidence_count': sum(1 for c in confidences if c < 0.6)
        }

def main():
    """主函数 - 用于测试"""
    classifier = SeasonLabelClassifier()
    
    # 测试样例
    test_products = [
        {
            '商品ID': 'test_001',
            '商品名': '男士短袖T恤透气运动衫',
            '类目': '服装',
            '商品描述': '夏季必备，透气舒适'
        },
        {
            '商品ID': 'test_002',
            '商品名': '羽绒服男士加厚保暖外套',
            '类目': '服装',
            '商品描述': '冬季保暖，防风防寒'
        },
        {
            '商品ID': 'test_003',
            '商品名': '苹果手机保护壳',
            '类目': '电器数码',
            '商品描述': '全面保护，四季适用'
        }
    ]
    
    print("🧪 测试季节标签分类器...")
    
    for product in test_products:
        result = classifier.classify_single(product)
        print(f"\n商品: {product['商品名']}")
        print(f"预测标签: {result['final_label']}")
        print(f"置信度: {result['confidence']:.2f}")
        print(f"推理依据: {result['reasoning']}")

if __name__ == "__main__":
    main()
