#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI客户端 - 用于调用Claude API进行季节标签分类
"""

import os
import json
import asyncio
import httpx
from typing import Dict, List, Optional, Any
import logging

class AIClient:
    """AI客户端 - 封装Claude API调用"""
    
    def __init__(self, config: Dict = None):
        """初始化AI客户端"""
        self.config = config or {}
        self.api_key = self.config.get('claude_api_key') or os.environ.get('CLAUDE_API_KEY')
        self.base_url = self.config.get('base_url') or "https://api.anthropic.com/v1/messages"
        self.model = self.config.get('model') or "claude-3-sonnet-20240229"
        self.timeout = self.config.get('timeout') or 30
        self.max_retries = self.config.get('max_retries') or 3
        self.logger = logging.getLogger("ai_client")
    
    async def predict(self, prompt: str) -> str:
        """调用Claude API进行预测"""
        if not self.api_key:
            raise ValueError("Claude API密钥未设置")
        
        headers = {
            "x-api-key": self.api_key,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 100,
            "temperature": 0.1
        }
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        self.base_url,
                        headers=headers,
                        json=data
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return result["content"][0]["text"]
                    else:
                        error_msg = f"API调用失败: {response.status_code} - {response.text}"
                        self.logger.error(error_msg)
                        
                        # 如果是最后一次尝试，抛出异常
                        if attempt == self.max_retries - 1:
                            raise Exception(error_msg)
                        
                        # 否则等待后重试
                        await asyncio.sleep(2 ** attempt)  # 指数退避
            
            except (httpx.RequestError, asyncio.TimeoutError) as e:
                error_msg = f"网络错误: {str(e)}"
                self.logger.error(error_msg)
                
                # 如果是最后一次尝试，抛出异常
                if attempt == self.max_retries - 1:
                    raise Exception(error_msg)
                
                # 否则等待后重试
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        # 如果所有尝试都失败
        raise Exception("所有API调用尝试均失败")
    
    async def batch_predict(self, prompts: List[str]) -> List[str]:
        """批量调用Claude API进行预测"""
        tasks = [self.predict(prompt) for prompt in prompts]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    def sync_predict(self, prompt: str) -> str:
        """同步版本的预测方法（用于不支持异步的环境）"""
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(self.predict(prompt))
        finally:
            loop.close()
    
    def sync_batch_predict(self, prompts: List[str]) -> List[str]:
        """同步版本的批量预测方法"""
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(self.batch_predict(prompts))
        finally:
            loop.close()
    
    def test_connection(self) -> bool:
        """测试API连接是否正常"""
        try:
            result = self.sync_predict("Hello, Claude. Please respond with 'OK'.")
            return "OK" in result
        except Exception as e:
            self.logger.error(f"连接测试失败: {str(e)}")
            return False

# 简单测试
async def test_ai_client():
    """测试AI客户端"""
    # 从环境变量获取API密钥
    api_key = os.environ.get('CLAUDE_API_KEY')
    if not api_key:
        print("❌ 未设置CLAUDE_API_KEY环境变量")
        return
    
    config = {
        "claude_api_key": api_key,
        "model": "claude-3-sonnet-20240229",
        "timeout": 30
    }
    
    client = AIClient(config)
    
    # 测试单个预测
    prompt = """
    你是一个专业的电商商品季节性分类专家。请根据商品信息判断季节标签，只能输出以下三个标签之一：全季、SS、AW
    
    商品信息：
    商品名称：男士短袖T恤透气运动衫
    商品类目：服装
    商品描述：夏季必备，透气舒适
    
    请只返回标签名称（全季、SS、AW），不要添加任何解释。
    """
    
    try:
        print("🔄 测试AI客户端...")
        result = await client.predict(prompt)
        print(f"✅ 预测结果: {result}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_ai_client())
