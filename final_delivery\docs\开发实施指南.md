# 季节标签分类Agent - 开发实施指南

## 📋 开发准备

### 技术栈选择
- **后端框架**: FastAPI（推荐）或Flask
- **数据库**: PostgreSQL（主库）+ Redis（缓存）
- **AI服务**: Claude API + 自研关键词引擎
- **部署**: Docker + Kubernetes
- **监控**: Prometheus + Grafana + ELK

### 开发环境搭建
```bash
# 1. 创建项目目录
mkdir season-label-agent
cd season-label-agent

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install fastapi uvicorn pandas openpyxl openai redis psycopg2-binary
pip install prometheus-client python-multipart aiofiles

# 4. 创建项目结构
mkdir -p {app,tests,docs,config,data,logs}
```

---

## 🏗️ 项目结构

```
season-label-agent/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── api/
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── classify.py     # 分类接口
│   │   │   ├── feedback.py     # 反馈接口
│   │   │   └── monitor.py      # 监控接口
│   ├── core/
│   │   ├── __init__.py
│   │   ├── classifier.py       # 核心分类器
│   │   ├── keyword_engine.py   # 关键词引擎
│   │   ├── ai_client.py        # AI客户端
│   │   └── validator.py        # 结果验证器
│   ├── models/
│   │   ├── __init__.py
│   │   ├── database.py         # 数据库模型
│   │   └── schemas.py          # API模型
│   ├── services/
│   │   ├── __init__.py
│   │   ├── classification.py   # 分类服务
│   │   ├── feedback.py         # 反馈服务
│   │   └── monitoring.py       # 监控服务
│   └── utils/
│       ├── __init__.py
│       ├── config.py           # 配置管理
│       ├── logger.py           # 日志工具
│       └── cache.py            # 缓存工具
├── tests/
│   ├── __init__.py
│   ├── test_classifier.py
│   └── test_api.py
├── config/
│   ├── config.yaml
│   └── rules.json
├── data/
│   ├── keywords/
│   └── samples/
├── docs/
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── README.md
```

---

## 🔧 核心模块实现

### 1. 核心分类器 (app/core/classifier.py)
```python
from typing import Dict, Tuple
import asyncio
from .keyword_engine import KeywordEngine
from .ai_client import AIClient
from .validator import ResultValidator

class SeasonClassifier:
    def __init__(self, config: Dict):
        self.keyword_engine = KeywordEngine(config)
        self.ai_client = AIClient(config)
        self.validator = ResultValidator(config)
    
    async def classify_single(self, product_info: Dict) -> Dict:
        """单个商品分类"""
        # 关键词匹配
        keyword_result = self.keyword_engine.match(product_info)
        
        # AI推理
        ai_result = await self.ai_client.predict(product_info)
        
        # 结果验证
        final_result = self.validator.validate(
            keyword_result, ai_result, product_info
        )
        
        return {
            'product_id': product_info.get('id'),
            'keyword_prediction': keyword_result['label'],
            'ai_prediction': ai_result['label'],
            'final_label': final_result['label'],
            'confidence': final_result['confidence'],
            'reasoning': final_result['reasoning']
        }
    
    async def classify_batch(self, products: List[Dict]) -> List[Dict]:
        """批量分类"""
        tasks = [self.classify_single(product) for product in products]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'product_id': products[i].get('id'),
                    'error': str(result),
                    'status': 'failed'
                })
            else:
                result['status'] = 'success'
                processed_results.append(result)
        
        return processed_results
```

### 2. 关键词引擎 (app/core/keyword_engine.py)
```python
import json
import re
from typing import Dict, List

class KeywordEngine:
    def __init__(self, config: Dict):
        self.config = config
        self.rules = self._load_rules()
    
    def _load_rules(self) -> Dict:
        """加载关键词规则"""
        with open('config/rules.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def match(self, product_info: Dict) -> Dict:
        """关键词匹配"""
        product_name = product_info.get('name', '').lower()
        category = product_info.get('category', '').lower()
        description = product_info.get('description', '').lower()
        
        # 检查全季类目
        if self._is_all_season_category(category):
            return {'label': '全季', 'confidence': 0.9, 'reason': '全季类目'}
        
        # 检查SS关键词
        ss_score = self._calculate_keyword_score(
            product_name + ' ' + description, 
            self.rules['ss_keywords']
        )
        
        # 检查AW关键词
        aw_score = self._calculate_keyword_score(
            product_name + ' ' + description,
            self.rules['aw_keywords']
        )
        
        # 判断结果
        if ss_score > aw_score and ss_score > 0.5:
            return {'label': 'SS', 'confidence': ss_score, 'reason': 'SS关键词匹配'}
        elif aw_score > ss_score and aw_score > 0.5:
            return {'label': 'AW', 'confidence': aw_score, 'reason': 'AW关键词匹配'}
        else:
            return {'label': '全季', 'confidence': 0.7, 'reason': '无明显季节性特征'}
    
    def _is_all_season_category(self, category: str) -> bool:
        """检查是否为全季类目"""
        all_season_categories = self.rules.get('all_season_categories', [])
        return any(cat in category for cat in all_season_categories)
    
    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """计算关键词匹配分数"""
        matches = sum(1 for keyword in keywords if keyword in text)
        return min(matches / len(keywords) * 2, 1.0)  # 归一化到0-1
```

### 3. API接口 (app/api/v1/classify.py)
```python
from fastapi import APIRouter, UploadFile, File, BackgroundTasks, HTTPException
from fastapi.responses import FileResponse
from typing import List
import pandas as pd
import uuid
from ...services.classification import ClassificationService
from ...models.schemas import ClassificationRequest, ClassificationResponse

router = APIRouter()
classification_service = ClassificationService()

@router.post("/batch", response_model=dict)
async def classify_batch(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """批量分类接口"""
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持Excel文件")
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 保存上传文件
    file_path = f"data/uploads/{task_id}_{file.filename}"
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # 后台处理
    background_tasks.add_task(
        classification_service.process_batch_file,
        task_id, file_path
    )
    
    return {
        "task_id": task_id,
        "status": "processing",
        "message": "文件上传成功，正在处理中"
    }

@router.get("/result/{task_id}")
async def get_classification_result(task_id: str):
    """获取分类结果"""
    result = await classification_service.get_task_result(task_id)
    if not result:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return result

@router.post("/single", response_model=ClassificationResponse)
async def classify_single(request: ClassificationRequest):
    """单个商品分类"""
    result = await classification_service.classify_single(request.dict())
    return ClassificationResponse(**result)

@router.get("/download/{task_id}")
async def download_result(task_id: str):
    """下载结果文件"""
    file_path = f"data/results/{task_id}_result.xlsx"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="结果文件不存在")
    
    return FileResponse(
        file_path,
        filename=f"classification_result_{task_id}.xlsx",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
```

---

## 📊 数据库设计

### 表结构设计
```sql
-- 分类任务表
CREATE TABLE classification_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(50) UNIQUE NOT NULL,
    filename VARCHAR(255),
    total_count INTEGER,
    processed_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'processing',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    result_file_path VARCHAR(500)
);

-- 分类结果表
CREATE TABLE classification_results (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(50) REFERENCES classification_tasks(task_id),
    product_id VARCHAR(100),
    product_name TEXT,
    category VARCHAR(200),
    manual_label VARCHAR(10),
    keyword_prediction VARCHAR(10),
    ai_prediction VARCHAR(10),
    final_label VARCHAR(10),
    confidence DECIMAL(4,3),
    reasoning TEXT,
    is_match BOOLEAN,
    processing_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 反馈记录表
CREATE TABLE feedback_records (
    id BIGSERIAL PRIMARY KEY,
    product_id VARCHAR(100),
    original_prediction VARCHAR(10),
    human_judgment VARCHAR(10),
    feedback_type VARCHAR(20), -- 'correct', 'incorrect', 'uncertain'
    feedback_reason TEXT,
    operator VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 关键词规则表
CREATE TABLE keyword_rules (
    id BIGSERIAL PRIMARY KEY,
    rule_type VARCHAR(20), -- 'ss', 'aw', 'all_season'
    keyword VARCHAR(100),
    weight DECIMAL(3,2) DEFAULT 1.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 性能监控表
CREATE TABLE performance_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_date DATE,
    total_processed INTEGER,
    accuracy_rate DECIMAL(5,4),
    ss_accuracy DECIMAL(5,4),
    aw_accuracy DECIMAL(5,4),
    all_season_accuracy DECIMAL(5,4),
    avg_processing_time_ms INTEGER,
    error_rate DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_classification_results_task_id ON classification_results(task_id);
CREATE INDEX idx_classification_results_product_id ON classification_results(product_id);
CREATE INDEX idx_feedback_records_product_id ON feedback_records(product_id);
CREATE INDEX idx_performance_metrics_date ON performance_metrics(metric_date);
```

---

## 🔄 部署配置

### Docker配置
```dockerfile
# Dockerfile
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data/uploads data/results logs

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  season-agent:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**********************************************/season_agent
      - REDIS_URL=redis://redis:6379/0
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=season_agent
      - POSTGRES_USER=agent_user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  postgres_data:
  grafana_data:
```

---

## 📈 监控配置

### Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'season-agent'
    static_configs:
      - targets: ['season-agent:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 告警规则
```yaml
# monitoring/alert_rules.yml
groups:
  - name: season_agent_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(classification_errors_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "分类错误率过高"
          description: "过去5分钟分类错误率超过5%"

      - alert: LowAccuracy
        expr: classification_accuracy < 0.90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "分类准确率过低"
          description: "分类准确率低于90%"

      - alert: ServiceDown
        expr: up{job="season-agent"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务不可用"
          description: "季节标签分类服务已停止"
```

---

## 🧪 测试策略

### 单元测试
```python
# tests/test_classifier.py
import pytest
from app.core.classifier import SeasonClassifier

@pytest.fixture
def classifier():
    config = {
        'claude_api_key': 'test_key',
        'base_url': 'test_url'
    }
    return SeasonClassifier(config)

def test_classify_obvious_ss_product(classifier):
    product = {
        'id': 'test_001',
        'name': '男士短袖T恤透气运动衫',
        'category': '服装',
        'description': '夏季必备，透气舒适'
    }
    
    result = classifier.classify_single(product)
    assert result['final_label'] == 'SS'
    assert result['confidence'] > 0.8

def test_classify_obvious_aw_product(classifier):
    product = {
        'id': 'test_002',
        'name': '羽绒服男士加厚保暖外套',
        'category': '服装',
        'description': '冬季保暖，防风防寒'
    }
    
    result = classifier.classify_single(product)
    assert result['final_label'] == 'AW'
    assert result['confidence'] > 0.8
```

### 集成测试
```python
# tests/test_api.py
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_classify_single_api():
    response = client.post("/api/v1/classify/single", json={
        "id": "test_001",
        "name": "短袖T恤",
        "category": "服装",
        "description": "夏季透气"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["final_label"] in ["全季", "SS", "AW"]
    assert 0 <= data["confidence"] <= 1

def test_batch_upload():
    with open("tests/sample.xlsx", "rb") as f:
        response = client.post(
            "/api/v1/classify/batch",
            files={"file": ("sample.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        )
    
    assert response.status_code == 200
    data = response.json()
    assert "task_id" in data
    assert data["status"] == "processing"
```

---

## 📋 上线检查清单

### 开发完成检查
- [ ] 核心分类功能实现
- [ ] API接口开发完成
- [ ] 数据库设计和初始化
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 性能测试达标

### 部署准备检查
- [ ] Docker镜像构建成功
- [ ] 配置文件准备完成
- [ ] 数据库迁移脚本
- [ ] 监控配置就绪
- [ ] 日志配置完成
- [ ] 备份策略制定

### 生产环境检查
- [ ] 服务器资源充足
- [ ] 网络连通性测试
- [ ] 安全配置检查
- [ ] 权限配置正确
- [ ] 监控告警测试
- [ ] 回滚方案准备

### 业务验收检查
- [ ] 功能演示通过
- [ ] 性能指标达标
- [ ] 用户培训完成
- [ ] 文档交付完整
- [ ] 运维手册准备
- [ ] 应急预案制定

---

**文档版本**: v1.0  
**创建时间**: 2025年7月17日  
**适用环境**: 生产环境  
**维护团队**: 技术开发团队
