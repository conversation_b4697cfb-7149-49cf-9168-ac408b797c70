#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分层抽样功能 - 小规模测试
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime

def analyze_detailed_mismatch(row, keyword_predicted, ai_predicted, manual_label):
    """详细分析不匹配原因"""
    product_name = str(row['商品名']).lower()
    category = str(row['类目']).lower()
    
    # 强季节性关键词
    strong_aw_keywords = ['羽绒', '冬季', '保暖', '加厚', '防风', '毛呢', '皮草', '雪地靴', '棉拖', '冲锋衣']
    strong_ss_keywords = ['夏季', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气', '短裤', '短裙']
    
    # 检查关键词
    aw_keywords_found = [kw for kw in strong_aw_keywords if kw in product_name]
    ss_keywords_found = [kw for kw in strong_ss_keywords if kw in product_name]
    
    reason = ""
    mismatch_type = ""
    confidence = "低"
    possible_error = "否"
    
    # 详细分析各种情况
    if manual_label == '全季' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含明显AW关键词{aw_keywords_found}，AI判断为AW，但人工标记为全季"
            mismatch_type = "明显AW商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显AW特征"
        else:
            reason = f"AI基于商品特征判断为AW，可能考虑了类目等因素"
            mismatch_type = "AW判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == '全季' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含明显SS关键词{ss_keywords_found}，AI判断为SS，但人工标记为全季"
            mismatch_type = "明显SS商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显SS特征"
        else:
            reason = f"AI基于商品特征判断为SS，可能考虑了类目等因素"
            mismatch_type = "SS判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label in ['AW', 'SS'] and ai_predicted == '全季':
        reason = f"AI判断为全季，但人工标记为{manual_label}"
        mismatch_type = f"全季商品误标为{manual_label}"
        confidence = "中"
        possible_error = "可能"
    
    elif manual_label == 'AW' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含SS关键词{ss_keywords_found}，AI判断为SS，但人工标记为AW"
            mismatch_type = "季节标签错误(AW->SS)"
            confidence = "高"
            possible_error = "是 - 季节标签错误"
        else:
            reason = f"AI判断为SS与人工标记AW不一致"
            mismatch_type = "季节判断分歧(AW vs SS)"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == 'SS' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含AW关键词{aw_keywords_found}，AI判断为AW，但人工标记为SS"
            mismatch_type = "季节标签错误(SS->AW)"
            confidence = "高"
            possible_error = "是 - 季节标签错误"
        else:
            reason = f"AI判断为AW与人工标记SS不一致"
            mismatch_type = "季节判断分歧(SS vs AW)"
            confidence = "中"
            possible_error = "可能"
    
    # 如果关键词预测和AI预测一致，提高置信度
    if keyword_predicted == ai_predicted and keyword_predicted != manual_label:
        if confidence == "中":
            confidence = "高"
        elif confidence == "低":
            confidence = "中"
        if possible_error == "否":
            possible_error = "可能"
    
    return reason, mismatch_type, confidence, possible_error

def main():
    """主函数 - 小规模测试"""
    print("="*80)
    print("分层抽样测试 - 每个标签10条，总计30条")
    print("="*80)
    
    try:
        # 读取数据
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")
        
        # 重命名列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })
        
        # 查看标签分布
        label_counts = df['人工标签'].value_counts()
        print(f"\n原始数据标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} 条")
        
        # 分层抽样 - 每个标签10条
        sample_dfs = []
        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) >= 10:
                label_sample = label_data.sample(n=10, random_state=42)
                sample_dfs.append(label_sample)
                print(f"  {label}: 抽样 10 条")
            else:
                sample_dfs.append(label_data)
                print(f"  {label}: 全部 {len(label_data)} 条")
        
        # 合并样本
        sample_df = pd.concat(sample_dfs, ignore_index=True)
        sample_df = sample_df.sample(frac=1, random_state=42).reset_index(drop=True)
        print(f"\n总抽样数据: {len(sample_df)} 条")
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 添加分析列
        sample_df['关键词预测'] = ''
        sample_df['AI预测'] = ''
        sample_df['匹配情况'] = ''
        sample_df['不匹配原因'] = ''
        sample_df['不匹配类型'] = ''
        sample_df['可能的标记错误'] = ''
        sample_df['置信度'] = ''
        
        print(f"\n开始处理...")
        
        # 逐个处理
        for idx, row in sample_df.iterrows():
            product_info = {
                '商品ID': row['商品ID'],
                '商品名': row['商品名'],
                '类目': row['类目'],
                '商品描述': row.get('商品描述', '')
            }
            
            try:
                # 关键词分类
                keyword_result = classifier.classify_single_product(product_info)
                sample_df.at[idx, '关键词预测'] = keyword_result
                
                # AI分类
                ai_result = classifier.classify_with_ai(product_info)
                sample_df.at[idx, 'AI预测'] = ai_result
                
                # 分析匹配情况
                manual_label = str(row['人工标签'])
                
                if manual_label == ai_result:
                    sample_df.at[idx, '匹配情况'] = '匹配'
                    sample_df.at[idx, '不匹配原因'] = ''
                    sample_df.at[idx, '不匹配类型'] = ''
                    sample_df.at[idx, '可能的标记错误'] = '否'
                    sample_df.at[idx, '置信度'] = ''
                else:
                    sample_df.at[idx, '匹配情况'] = '不匹配'
                    
                    # 分析不匹配原因
                    reason, mismatch_type, confidence, possible_error = analyze_detailed_mismatch(
                        row, keyword_result, ai_result, manual_label
                    )
                    
                    sample_df.at[idx, '不匹配原因'] = reason
                    sample_df.at[idx, '不匹配类型'] = mismatch_type
                    sample_df.at[idx, '置信度'] = confidence
                    sample_df.at[idx, '可能的标记错误'] = possible_error
                
                print(f"  处理完成: {row['商品ID']} - {row['商品名'][:20]}... -> {manual_label} vs {ai_result}")
                
            except Exception as e:
                print(f"  处理失败: {row['商品ID']} - {e}")
                sample_df.at[idx, 'AI预测'] = keyword_result
                sample_df.at[idx, '不匹配原因'] = f"处理失败: {str(e)}"
        
        # 生成分析报告
        print(f"\n" + "="*80)
        print("分析结果")
        print("="*80)
        
        # 基本统计
        total_count = len(sample_df)
        match_count = (sample_df['匹配情况'] == '匹配').sum()
        mismatch_count = total_count - match_count
        
        print(f"总样本数: {total_count}")
        print(f"匹配数: {match_count} ({match_count/total_count:.1%})")
        print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count:.1%})")
        
        # 各标签准确率
        print(f"\n各标签准确率:")
        for label in ['全季', 'SS', 'AW']:
            label_data = sample_df[sample_df['人工标签'] == label]
            if len(label_data) > 0:
                label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
                accuracy = label_correct / len(label_data)
                print(f"  {label}: {accuracy:.1%} ({label_correct}/{len(label_data)})")
        
        # 不匹配分析
        mismatched_data = sample_df[sample_df['匹配情况'] == '不匹配']
        if len(mismatched_data) > 0:
            print(f"\n不匹配类型分布:")
            mismatch_types = mismatched_data['不匹配类型'].value_counts()
            for mtype, count in mismatch_types.items():
                print(f"  {mtype}: {count} 条")
            
            print(f"\n不匹配详情:")
            detail_cols = ['商品ID', '商品名', '人工标签', 'AI预测', '不匹配类型', '置信度', '可能的标记错误']
            print(mismatched_data[detail_cols].to_string(index=False))
        
        # 保存结果
        output_file = f"分层抽样测试结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        sample_df.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 可能的标记错误
        possible_errors = sample_df[sample_df['可能的标记错误'].str.contains('是', na=False)]
        if len(possible_errors) > 0:
            print(f"\n发现 {len(possible_errors)} 个可能的标记错误:")
            error_cols = ['商品ID', '商品名', '人工标签', 'AI预测', '不匹配原因']
            print(possible_errors[error_cols].to_string(index=False))
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
