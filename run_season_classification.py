#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行季节标签分类系统
"""

import os
import sys
from datetime import datetime

def main():
    """主函数"""
    print("="*60)
    print("季节标签分类系统")
    print("="*60)
    print("功能：基于商品信息判断季节标签（全季、SS春夏、AW秋冬）")
    print("目标准确率：全季95%，SS90%，AW90%")
    print("="*60)
    
    # 检查必要文件
    required_files = [
        "season_label_classifier.py",
        "prompts/prompt",
        "上架商品季节标签.xlsx"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请确保所有文件都存在后再运行。")
        return
    
    print("所有必要文件检查完成 ✓")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式 - 使用内置测试数据验证系统")
    print("2. 样本模式 - 处理Excel文件的部分数据（50条）")
    print("3. 完整模式 - 处理Excel文件的全部数据")
    print("4. 检查Excel文件结构")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n运行测试模式...")
            from test_classifier import test_classifier
            test_classifier()
            
        elif choice == "2":
            print("\n运行样本模式...")
            from process_excel_data import process_excel_file, run_classification, analyze_results
            
            df = process_excel_file("上架商品季节标签.xlsx", sample_size=50)
            if df is not None:
                df_result = run_classification(df, use_ai=True, batch_size=5)
                analyze_results(df_result)
                
                # 保存结果
                result_file = f"季节标签分类结果_样本_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                df_result.to_excel(result_file, index=False)
                print(f"\n结果已保存到: {result_file}")
            
        elif choice == "3":
            print("\n运行完整模式...")
            confirm = input("这将处理所有数据，可能需要较长时间，确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                from process_excel_data import process_excel_file, run_classification, analyze_results
                
                df = process_excel_file("上架商品季节标签.xlsx")
                if df is not None:
                    df_result = run_classification(df, use_ai=True, batch_size=10)
                    analyze_results(df_result)
                    
                    # 保存结果
                    result_file = f"季节标签分类结果_完整_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    df_result.to_excel(result_file, index=False)
                    print(f"\n结果已保存到: {result_file}")
            else:
                print("已取消完整模式运行")
                
        elif choice == "4":
            print("\n检查Excel文件结构...")
            from process_excel_data import check_excel_structure
            check_excel_structure("上架商品季节标签.xlsx")
            
        else:
            print("无效选择，请重新运行程序")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
