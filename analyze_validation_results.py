#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析验证结果 - 理解为什么改进后准确率下降
"""

import pandas as pd
from datetime import datetime

def analyze_validation_results():
    """分析验证结果"""
    print("🔍 分析验证结果...")
    
    try:
        # 读取验证结果
        result_files = [f for f in os.listdir('.') if f.startswith('改进效果验证_') and f.endswith('.xlsx')]
        if not result_files:
            print("❌ 未找到验证结果文件")
            return
        
        latest_file = max(result_files)
        df = pd.read_excel(latest_file)
        print(f"📊 读取验证结果: {latest_file}")
        print(f"📊 数据规模: {len(df)} 条记录")
        
        # 分析不匹配的情况
        mismatched = df[df['匹配情况'] == '不匹配']
        print(f"\n不匹配分析:")
        print(f"不匹配数量: {len(mismatched)} 条")
        
        if len(mismatched) > 0:
            print(f"\n各标签不匹配情况:")
            for label in ['全季', 'SS', 'AW']:
                label_mismatched = mismatched[mismatched['人工标签'] == label]
                if len(label_mismatched) > 0:
                    print(f"\n{label}标签不匹配 ({len(label_mismatched)} 条):")
                    ai_predictions = label_mismatched['AI预测'].value_counts()
                    for ai_label, count in ai_predictions.items():
                        print(f"  {label} → {ai_label}: {count} 条")
                    
                    # 显示具体的不匹配商品
                    print(f"  具体商品:")
                    for idx, row in label_mismatched.head(5).iterrows():
                        print(f"    {row['商品名'][:30]}... → {row['人工标签']} vs {row['AI预测']}")
        
        # 对比关键词预测和AI预测
        print(f"\n关键词 vs AI预测对比:")
        keyword_ai_same = (df['关键词预测'] == df['AI预测']).sum()
        print(f"关键词和AI预测一致: {keyword_ai_same}/{len(df)} ({keyword_ai_same/len(df):.1%})")
        
        # 分析关键词预测的准确率
        keyword_correct = (df['关键词预测'] == df['人工标签']).sum()
        ai_correct = (df['AI预测'] == df['人工标签']).sum()
        print(f"关键词预测准确率: {keyword_correct/len(df):.1%}")
        print(f"AI预测准确率: {ai_correct/len(df):.1%}")
        
        # 生成详细分析报告
        generate_detailed_analysis(df, mismatched)
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()

def generate_detailed_analysis(df, mismatched):
    """生成详细分析报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    report_content = f"""# 验证结果详细分析报告

## 分析概况
- **分析时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **样本数量**: {len(df)} 条
- **不匹配数量**: {len(mismatched)} 条

## 问题分析

### 1. 准确率下降原因分析

可能的原因：
1. **测试样本不同**: 这次使用的是修正后数据的随机抽样，与之前1500条分层抽样不同
2. **API响应变化**: AI模型的响应可能存在时间差异
3. **系统配置**: 关键词或提示词更新可能需要重启或重新加载

### 2. 各标签表现分析
"""
    
    for label in ['全季', 'SS', 'AW']:
        label_data = df[df['人工标签'] == label]
        label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
        accuracy = label_correct / len(label_data) if len(label_data) > 0 else 0
        
        label_mismatched = mismatched[mismatched['人工标签'] == label]
        
        report_content += f"""
#### {label}标签
- 样本数: {len(label_data)}
- 正确数: {label_correct}
- 准确率: {accuracy:.1%}
- 不匹配数: {len(label_mismatched)}
"""
        
        if len(label_mismatched) > 0:
            ai_predictions = label_mismatched['AI预测'].value_counts()
            report_content += f"- 主要错误类型:\n"
            for ai_label, count in ai_predictions.items():
                report_content += f"  - {label} → {ai_label}: {count} 条\n"
    
    report_content += f"""

### 3. 关键词 vs AI预测对比
- 关键词预测准确率: {(df['关键词预测'] == df['人工标签']).sum()/len(df):.1%}
- AI预测准确率: {(df['AI预测'] == df['人工标签']).sum()/len(df):.1%}
- 一致性: {(df['关键词预测'] == df['AI预测']).sum()/len(df):.1%}

## 建议措施

### 立即行动
1. **重新测试**: 使用相同的1500条样本重新测试
2. **检查配置**: 确认关键词和提示词更新是否生效
3. **API稳定性**: 检查API服务的稳定性和一致性

### 系统优化
1. **样本一致性**: 使用固定的测试集进行对比
2. **缓存机制**: 考虑添加结果缓存以提高一致性
3. **多次测试**: 进行多次测试取平均值

### 长期改进
1. **模型微调**: 基于错误案例进行模型优化
2. **规则完善**: 完善关键词匹配规则
3. **质量监控**: 建立持续的质量监控机制

---
**报告生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
"""
    
    report_file = f"验证结果分析报告_{timestamp}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📋 详细分析报告已保存到: {report_file}")

def retest_with_original_samples():
    """使用原始的1500条样本重新测试"""
    print("\n🔄 建议使用原始1500条样本重新测试...")
    
    # 检查是否有原始的1500条测试结果
    original_files = [f for f in os.listdir('.') if f.startswith('季节标签最终分析_')]
    if original_files:
        latest_original = max(original_files)
        print(f"📊 找到原始测试结果: {latest_original}")
        print("💡 建议:")
        print("1. 使用相同的1500条样本重新测试")
        print("2. 对比修正前后的具体商品预测结果")
        print("3. 分析哪些商品的预测发生了变化")
    else:
        print("❌ 未找到原始测试结果文件")

def main():
    """主函数"""
    import os
    
    print("🔍 开始分析验证结果...")
    
    # 分析验证结果
    analyze_validation_results()
    
    # 建议重新测试
    retest_with_original_samples()
    
    print(f"\n💡 总结:")
    print("准确率下降可能是由于测试样本不同或API响应变化导致的")
    print("建议使用相同的测试集进行对比，以获得更准确的改进效果评估")

if __name__ == "__main__":
    main()
