#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理器 - 用于处理大量商品的季节标签分类
"""

import pandas as pd
import asyncio
import os
from datetime import datetime
from typing import Dict, List, Optional
import logging
from tqdm import tqdm

from .season_label_classifier import SeasonLabelClassifier
from .ai_client import AIClient

class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config: Dict = None):
        """初始化批量处理器"""
        self.config = config or {}
        self.classifier = SeasonLabelClassifier()
        self.ai_client = AIClient(config) if config.get('claude_api_key') else None
        self.batch_size = self.config.get('batch_size', 20)
        self.max_concurrent = self.config.get('max_concurrent', 5)
        self.logger = logging.getLogger("batch_processor")
        
        # 如果有AI客户端，设置到分类器中
        if self.ai_client:
            self.classifier.ai_client = self.ai_client
    
    def load_excel_file(self, file_path: str) -> pd.DataFrame:
        """加载Excel文件"""
        try:
            df = pd.read_excel(file_path)
            self.logger.info(f"成功加载文件: {file_path}, 共{len(df)}条记录")
            return df
        except Exception as e:
            self.logger.error(f"加载文件失败: {str(e)}")
            raise
    
    def preprocess_data(self, df: pd.DataFrame) -> List[Dict]:
        """预处理数据"""
        products = []
        
        for idx, row in df.iterrows():
            product = {
                '商品ID': row.get('商品ID', f'product_{idx}'),
                '商品名': str(row.get('商品名', '')),
                '类目': str(row.get('类目', '')),
                '商品描述': str(row.get('商品描述', '')),
                '人工标签': str(row.get('人工标签', '')),
                '原始行号': idx
            }
            products.append(product)
        
        return products
    
    async def process_batch_async(self, products: List[Dict]) -> List[Dict]:
        """异步批量处理"""
        results = []
        
        # 分批处理
        for i in range(0, len(products), self.batch_size):
            batch = products[i:i + self.batch_size]
            
            # 创建异步任务
            tasks = []
            for product in batch:
                task = self.process_single_product_async(product)
                tasks.append(task)
            
            # 限制并发数
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def limited_task(task):
                async with semaphore:
                    return await task
            
            # 执行批次任务
            batch_results = await asyncio.gather(
                *[limited_task(task) for task in tasks],
                return_exceptions=True
            )
            
            # 处理结果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    # 处理异常情况
                    error_result = {
                        'product_id': batch[j].get('商品ID'),
                        'error': str(result),
                        'status': 'failed'
                    }
                    results.append(error_result)
                else:
                    result['status'] = 'success'
                    results.append(result)
            
            # 显示进度
            processed = min(i + self.batch_size, len(products))
            print(f"已处理: {processed}/{len(products)} ({processed/len(products)*100:.1f}%)")
        
        return results
    
    async def process_single_product_async(self, product: Dict) -> Dict:
        """异步处理单个商品"""
        # 关键词分类
        keyword_result = self.classifier.classify_by_keywords(product)
        
        # AI分类（如果可用）
        ai_result = keyword_result  # 默认使用关键词结果
        if self.ai_client:
            try:
                ai_result = await self.classifier.classify_by_ai(product)
            except Exception as e:
                self.logger.warning(f"AI分类失败，使用关键词结果: {str(e)}")
        
        # 结果验证和合并
        final_result = self.merge_results(keyword_result, ai_result, product)
        
        return final_result
    
    def process_batch_sync(self, products: List[Dict]) -> List[Dict]:
        """同步批量处理"""
        results = []
        
        # 使用tqdm显示进度条
        for product in tqdm(products, desc="处理商品"):
            try:
                result = self.classifier.classify_single(product)
                
                # 添加不匹配分析
                if product.get('人工标签'):
                    mismatch_analysis = self.classifier.analyze_mismatch(
                        product['人工标签'],
                        result['final_label'],
                        product
                    )
                    result.update(mismatch_analysis)
                
                result['status'] = 'success'
                results.append(result)
                
            except Exception as e:
                error_result = {
                    'product_id': product.get('商品ID'),
                    'error': str(e),
                    'status': 'failed'
                }
                results.append(error_result)
        
        return results
    
    def merge_results(self, keyword_result: Dict, ai_result: Dict, product: Dict) -> Dict:
        """合并关键词和AI结果"""
        # 如果两者一致，使用较高的置信度
        if keyword_result['label'] == ai_result['label']:
            confidence = max(keyword_result['confidence'], ai_result['confidence'])
            reasoning = f"关键词+AI一致: {keyword_result['reason']}"
        else:
            # 如果不一致，使用置信度更高的结果
            if keyword_result['confidence'] > ai_result['confidence']:
                final_label = keyword_result['label']
                confidence = keyword_result['confidence']
                reasoning = f"关键词优先: {keyword_result['reason']}"
            else:
                final_label = ai_result['label']
                confidence = ai_result['confidence']
                reasoning = f"AI优先: {ai_result['reason']}"
        
        return {
            'product_id': product.get('商品ID'),
            'product_name': product.get('商品名'),
            'category': product.get('类目'),
            'manual_label': product.get('人工标签'),
            'keyword_prediction': keyword_result['label'],
            'ai_prediction': ai_result['label'],
            'final_label': final_label,
            'confidence': confidence,
            'reasoning': reasoning,
            'match_status': '匹配' if product.get('人工标签') == final_label else '不匹配'
        }
    
    def save_results(self, results: List[Dict], output_path: str):
        """保存结果到Excel文件"""
        df = pd.DataFrame(results)
        
        # 添加统计信息
        total_count = len(results)
        success_count = sum(1 for r in results if r.get('status') == 'success')
        
        if '人工标签' in df.columns:
            match_count = sum(1 for r in results if r.get('match_status') == '匹配')
            accuracy = match_count / total_count if total_count > 0 else 0
            
            # 添加汇总信息到第一行
            summary_row = {
                'product_id': 'SUMMARY',
                'product_name': f'总计:{total_count}, 成功:{success_count}, 准确率:{accuracy:.1%}',
                'final_label': f'匹配:{match_count}, 不匹配:{total_count-match_count}'
            }
            
            # 插入汇总行
            df = pd.concat([pd.DataFrame([summary_row]), df], ignore_index=True)
        
        # 保存文件
        df.to_excel(output_path, index=False)
        self.logger.info(f"结果已保存到: {output_path}")
    
    def generate_analysis_report(self, results: List[Dict]) -> Dict:
        """生成分析报告"""
        return self.classifier.generate_analysis_report(results)
    
    def process_file(self, input_path: str, output_path: str = None, use_async: bool = False) -> str:
        """处理文件的主入口"""
        # 生成输出文件名
        if not output_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = f"季节标签分类结果_{base_name}_{timestamp}.xlsx"
        
        print(f"🔄 开始处理文件: {input_path}")
        print(f"📊 输出文件: {output_path}")
        
        # 加载数据
        df = self.load_excel_file(input_path)
        products = self.preprocess_data(df)
        
        print(f"📦 共需处理 {len(products)} 个商品")
        
        # 处理数据
        if use_async and self.ai_client:
            print("🚀 使用异步模式处理...")
            results = asyncio.run(self.process_batch_async(products))
        else:
            print("🔄 使用同步模式处理...")
            results = self.process_batch_sync(products)
        
        # 保存结果
        self.save_results(results, output_path)
        
        # 生成分析报告
        report = self.generate_analysis_report(results)
        print(f"\n📊 处理完成统计:")
        print(f"总商品数: {report.get('total_count', 0)}")
        print(f"准确率: {report.get('accuracy', 0):.1%}")
        print(f"平均置信度: {report.get('average_confidence', 0):.2f}")
        
        return output_path

def main():
    """主函数 - 用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='季节标签批量分类处理器')
    parser.add_argument('input_file', help='输入Excel文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--async', action='store_true', help='使用异步模式')
    parser.add_argument('--api-key', help='Claude API密钥')
    parser.add_argument('--batch-size', type=int, default=20, help='批处理大小')
    
    args = parser.parse_args()
    
    # 配置
    config = {
        'batch_size': args.batch_size,
        'max_concurrent': 5
    }
    
    if args.api_key:
        config['claude_api_key'] = args.api_key
    
    # 创建处理器
    processor = BatchProcessor(config)
    
    # 处理文件
    try:
        output_file = processor.process_file(
            args.input_file,
            args.output,
            args.async
        )
        print(f"✅ 处理完成，结果保存到: {output_file}")
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")

if __name__ == "__main__":
    main()
