# 季节标签分类Agent - API接口文档

## 📋 接口概述

季节标签分类Agent提供了完整的RESTful API接口，支持单个商品分类、批量处理、结果查询等功能。

**API版本**: v1.0  
**基础URL**: `http://your-domain.com/api/v1`  
**认证方式**: API Key  
**数据格式**: JSON  

---

## 🔐 认证方式

### API Key认证
在请求头中添加API密钥：
```http
Authorization: Bearer your_api_key_here
Content-Type: application/json
```

### 获取API Key
联系系统管理员获取API密钥，或通过管理后台申请。

---

## 📊 核心接口

### 1. 单个商品分类

#### 接口信息
- **URL**: `/classify/single`
- **方法**: `POST`
- **描述**: 对单个商品进行季节标签分类

#### 请求参数
```json
{
  "product_id": "string",      // 商品ID（可选）
  "product_name": "string",    // 商品名称（必填）
  "category": "string",        // 商品类目（可选）
  "description": "string"      // 商品描述（可选）
}
```

#### 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "product_id": "test_001",
    "keyword_prediction": "SS",
    "ai_prediction": "SS",
    "final_label": "SS",
    "confidence": 0.95,
    "reasoning": "SS关键词匹配(分数:0.95)",
    "processing_time_ms": 150
  }
}
```

#### 示例请求
```bash
curl -X POST "http://your-domain.com/api/v1/classify/single" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "男士短袖T恤透气运动衫",
    "category": "服装",
    "description": "夏季必备，透气舒适"
  }'
```

### 2. 批量文件上传

#### 接口信息
- **URL**: `/classify/batch`
- **方法**: `POST`
- **描述**: 上传Excel文件进行批量分类处理

#### 请求参数
- **Content-Type**: `multipart/form-data`
- **file**: Excel文件（.xlsx/.xls格式）
- **use_async**: 是否使用异步处理（可选，默认true）

#### 响应结果
```json
{
  "code": 200,
  "message": "文件上传成功，正在处理中",
  "data": {
    "task_id": "uuid-string",
    "status": "processing",
    "total_count": 1500,
    "estimated_time": "5-10分钟"
  }
}
```

#### 示例请求
```bash
curl -X POST "http://your-domain.com/api/v1/classify/batch" \
  -H "Authorization: Bearer your_api_key" \
  -F "file=@商品数据.xlsx" \
  -F "use_async=true"
```

### 3. 任务状态查询

#### 接口信息
- **URL**: `/classify/task/{task_id}`
- **方法**: `GET`
- **描述**: 查询批量处理任务的状态和进度

#### 路径参数
- **task_id**: 任务ID（必填）

#### 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_id": "uuid-string",
    "status": "completed",  // processing, completed, failed
    "progress": {
      "total_count": 1500,
      "processed_count": 1500,
      "success_count": 1430,
      "error_count": 70,
      "percentage": 100
    },
    "result_summary": {
      "accuracy": 0.953,
      "avg_confidence": 0.87,
      "label_distribution": {
        "全季": 570,
        "SS": 476,
        "AW": 454
      }
    },
    "download_url": "/api/v1/classify/download/uuid-string",
    "created_at": "2025-07-17T16:21:10Z",
    "completed_at": "2025-07-17T16:26:30Z"
  }
}
```

### 4. 结果文件下载

#### 接口信息
- **URL**: `/classify/download/{task_id}`
- **方法**: `GET`
- **描述**: 下载批量处理的结果文件

#### 路径参数
- **task_id**: 任务ID（必填）

#### 响应结果
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **文件名**: `classification_result_{task_id}.xlsx`

#### 示例请求
```bash
curl -X GET "http://your-domain.com/api/v1/classify/download/uuid-string" \
  -H "Authorization: Bearer your_api_key" \
  -o result.xlsx
```

---

## 📈 监控接口

### 1. 健康检查

#### 接口信息
- **URL**: `/health`
- **方法**: `GET`
- **描述**: 检查系统健康状态

#### 响应结果
```json
{
  "status": "healthy",
  "timestamp": "2025-07-17T16:21:10Z",
  "version": "v1.0",
  "components": {
    "classifier": "healthy",
    "ai_client": "healthy",
    "database": "healthy"
  }
}
```

### 2. 系统指标

#### 接口信息
- **URL**: `/metrics`
- **方法**: `GET`
- **描述**: 获取系统性能指标

#### 响应结果
```json
{
  "code": 200,
  "data": {
    "total_requests": 15000,
    "success_rate": 0.998,
    "avg_response_time": 150,
    "current_accuracy": 0.953,
    "daily_processed": 5000,
    "queue_length": 0
  }
}
```

---

## 🔧 管理接口

### 1. 反馈提交

#### 接口信息
- **URL**: `/feedback`
- **方法**: `POST`
- **描述**: 提交人工反馈，用于系统优化

#### 请求参数
```json
{
  "product_id": "string",
  "original_prediction": "string",
  "human_judgment": "string",  // "correct", "incorrect"
  "correct_label": "string",   // 如果判断错误，提供正确标签
  "feedback_reason": "string", // 反馈原因
  "operator": "string"         // 操作人员
}
```

#### 响应结果
```json
{
  "code": 200,
  "message": "反馈提交成功",
  "data": {
    "feedback_id": "uuid-string",
    "status": "received"
  }
}
```

### 2. 规则更新

#### 接口信息
- **URL**: `/admin/rules`
- **方法**: `PUT`
- **描述**: 更新分类规则（需要管理员权限）

#### 请求参数
```json
{
  "rule_type": "keyword",  // keyword, category, threshold
  "action": "add",         // add, update, delete
  "data": {
    "keyword": "新关键词",
    "label": "SS",
    "weight": 0.8
  }
}
```

---

## 📋 数据格式说明

### 输入Excel格式要求
| 列名 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 商品ID | string | 否 | 商品唯一标识 |
| 商品名 | string | 是 | 商品名称 |
| 类目 | string | 否 | 商品类目 |
| 商品描述 | string | 否 | 商品详细描述 |
| 人工标签 | string | 否 | 用于准确率计算 |

### 输出Excel格式
| 列名 | 类型 | 说明 |
|------|------|------|
| 商品ID | string | 商品唯一标识 |
| 商品名 | string | 商品名称 |
| 类目 | string | 商品类目 |
| 人工标签 | string | 原始人工标签 |
| 关键词预测 | string | 关键词匹配结果 |
| AI预测 | string | AI模型预测结果 |
| 最终标签 | string | 最终分类结果 |
| 置信度 | float | 分类置信度(0-1) |
| 匹配状态 | string | 与人工标签匹配情况 |
| 推理依据 | string | 分类依据说明 |

---

## ⚠️ 错误码说明

### HTTP状态码
- **200**: 请求成功
- **400**: 请求参数错误
- **401**: 认证失败
- **403**: 权限不足
- **404**: 资源不存在
- **429**: 请求频率超限
- **500**: 服务器内部错误

### 业务错误码
```json
{
  "code": 4001,
  "message": "文件格式不支持",
  "data": null
}
```

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 4001 | 文件格式不支持 | 使用.xlsx或.xls格式 |
| 4002 | 文件大小超限 | 文件大小不超过10MB |
| 4003 | 必填字段缺失 | 检查商品名称字段 |
| 4004 | 任务不存在 | 检查task_id是否正确 |
| 5001 | AI服务不可用 | 稍后重试或联系管理员 |
| 5002 | 处理队列已满 | 稍后重试 |

---

## 🚀 使用示例

### Python SDK示例
```python
import requests
import json

class SeasonClassifierClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def classify_single(self, product_name, category=None, description=None):
        """单个商品分类"""
        url = f"{self.base_url}/classify/single"
        data = {
            'product_name': product_name,
            'category': category,
            'description': description
        }
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()
    
    def upload_batch(self, file_path):
        """批量文件上传"""
        url = f"{self.base_url}/classify/batch"
        headers = {'Authorization': self.headers['Authorization']}
        
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, headers=headers, files=files)
        return response.json()
    
    def get_task_status(self, task_id):
        """查询任务状态"""
        url = f"{self.base_url}/classify/task/{task_id}"
        response = requests.get(url, headers=self.headers)
        return response.json()

# 使用示例
client = SeasonClassifierClient(
    base_url="http://your-domain.com/api/v1",
    api_key="your_api_key"
)

# 单个分类
result = client.classify_single("男士短袖T恤")
print(f"分类结果: {result['data']['final_label']}")

# 批量处理
upload_result = client.upload_batch("商品数据.xlsx")
task_id = upload_result['data']['task_id']

# 查询状态
status = client.get_task_status(task_id)
print(f"处理进度: {status['data']['progress']['percentage']}%")
```

### JavaScript示例
```javascript
class SeasonClassifierClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        };
    }
    
    async classifySingle(productName, category = null, description = null) {
        const response = await fetch(`${this.baseUrl}/classify/single`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                product_name: productName,
                category: category,
                description: description
            })
        });
        return await response.json();
    }
    
    async uploadBatch(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch(`${this.baseUrl}/classify/batch`, {
            method: 'POST',
            headers: {
                'Authorization': this.headers.Authorization
            },
            body: formData
        });
        return await response.json();
    }
}

// 使用示例
const client = new SeasonClassifierClient(
    'http://your-domain.com/api/v1',
    'your_api_key'
);

// 单个分类
client.classifySingle('男士短袖T恤').then(result => {
    console.log('分类结果:', result.data.final_label);
});
```

---

## 📞 技术支持

**API文档版本**: v1.0  
**最后更新**: 2025年7月17日  
**技术支持**: <EMAIL>  
**问题反馈**: <EMAIL>  

**在线文档**: http://your-domain.com/docs  
**SDK下载**: http://your-domain.com/sdk  
**示例代码**: http://your-domain.com/examples
