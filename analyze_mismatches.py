#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析不匹配商品，判断是否为原始数据标记错误
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime
import re

def analyze_mismatch_reason(row, keyword_predicted, ai_predicted, manual_label):
    """分析不匹配的具体原因"""
    product_name = str(row['商品名']).lower()
    category = str(row['类目']).lower()
    description = str(row.get('商品描述', '')).lower()

    # 定义强季节性关键词
    strong_aw_keywords = ['羽绒', '冬季', '保暖', '加厚', '防风', '毛呢', '皮草', '雪地靴', '棉拖']
    strong_ss_keywords = ['夏季', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气']

    # 检查商品名中的强季节性特征
    has_strong_aw = any(keyword in product_name for keyword in strong_aw_keywords)
    has_strong_ss = any(keyword in product_name for keyword in strong_ss_keywords)

    # 分析不匹配原因
    reason = ""
    mismatch_type = ""
    confidence = ""
    possible_error = "否"

    if manual_label != ai_predicted:
        # 情况1：明显的季节性商品被标记为全季
        if manual_label == '全季' and ai_predicted in ['AW', 'SS']:
            if has_strong_aw and ai_predicted == 'AW':
                reason = f"商品名包含明显AW关键词({[kw for kw in strong_aw_keywords if kw in product_name]})，但被标记为全季"
                mismatch_type = "明显季节性商品误标为全季"
                confidence = "高"
                possible_error = "是 - 明显AW特征"
            elif has_strong_ss and ai_predicted == 'SS':
                reason = f"商品名包含明显SS关键词({[kw for kw in strong_ss_keywords if kw in product_name]})，但被标记为全季"
                mismatch_type = "明显季节性商品误标为全季"
                confidence = "高"
                possible_error = "是 - 明显SS特征"
            else:
                reason = f"AI判断为{ai_predicted}，可能基于商品特征或类目信息"
                mismatch_type = "季节性判断分歧"
                confidence = "中"
                possible_error = "可能"

        # 情况2：季节性商品被标记为错误的季节
        elif manual_label in ['AW', 'SS'] and ai_predicted in ['AW', 'SS'] and manual_label != ai_predicted:
            if has_strong_aw and ai_predicted == 'AW':
                reason = f"商品名明显是AW特征({[kw for kw in strong_aw_keywords if kw in product_name]})，但被标记为{manual_label}"
                mismatch_type = "季节标签错误"
                confidence = "高"
                possible_error = "是 - 季节标签错误"
            elif has_strong_ss and ai_predicted == 'SS':
                reason = f"商品名明显是SS特征({[kw for kw in strong_ss_keywords if kw in product_name]})，但被标记为{manual_label}"
                mismatch_type = "季节标签错误"
                confidence = "高"
                possible_error = "是 - 季节标签错误"
            else:
                reason = f"AI基于综合信息判断为{ai_predicted}，与人工标签{manual_label}不一致"
                mismatch_type = "季节判断分歧"
                confidence = "中"
                possible_error = "可能"

        # 情况3：全季商品被标记为季节性
        elif manual_label in ['AW', 'SS'] and ai_predicted == '全季':
            # 检查是否真的是全季商品
            all_season_categories = ['美妆个护', '食品', '电器', '居家日用', '母婴']
            is_all_season_category = any(cat in category for cat in all_season_categories)

            if is_all_season_category and not (has_strong_aw or has_strong_ss):
                reason = f"商品属于全季类目({category})且无明显季节性特征，可能被误标为{manual_label}"
                mismatch_type = "全季商品误标为季节性"
                confidence = "中"
                possible_error = "可能"
            else:
                reason = f"商品可能具有季节性特征，AI判断为全季与人工标签{manual_label}不一致"
                mismatch_type = "季节性判断分歧"
                confidence = "低"
                possible_error = "否"

        # 情况4：关键词和AI预测一致，但与人工不同
        if keyword_predicted == ai_predicted and keyword_predicted != manual_label:
            confidence = "高"
            if possible_error == "否":
                possible_error = "可能"

    return reason, mismatch_type, confidence, possible_error

def analyze_sample_data(samples_per_label=500):
    """分析样本数据中的不匹配情况 - 按标签分层抽样"""
    print("="*80)
    print("季节标签不匹配分析 - 分层抽样")
    print("="*80)

    try:
        # 读取Excel文件
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")

        # 重命名关键列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })

        # 查看各标签的分布
        label_counts = df['人工标签'].value_counts()
        print(f"\n原始数据标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} 条")

        # 按标签分层抽样
        sample_dfs = []
        total_sampled = 0

        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) > 0:
                # 如果该标签的数据少于目标样本数，则全部取出
                sample_size = min(samples_per_label, len(label_data))
                label_sample = label_data.sample(n=sample_size, random_state=42)
                sample_dfs.append(label_sample)
                total_sampled += sample_size
                print(f"  {label}: 抽样 {sample_size} 条 (总共 {len(label_data)} 条)")
            else:
                print(f"  {label}: 无数据")

        # 合并所有样本
        if sample_dfs:
            sample_df = pd.concat(sample_dfs, ignore_index=True)
            # 随机打乱顺序
            sample_df = sample_df.sample(frac=1, random_state=42).reset_index(drop=True)
            print(f"\n总共抽样 {len(sample_df)} 条数据进行分析")
        else:
            print("没有找到有效的标签数据")
            return None, None
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 添加预测列和分析列
        sample_df['关键词预测'] = ''
        sample_df['AI预测'] = ''
        sample_df['匹配情况'] = ''
        sample_df['不匹配原因'] = ''
        sample_df['不匹配类型'] = ''
        sample_df['可能的标记错误'] = ''
        sample_df['置信度'] = ''
        
        print(f"\n开始分类分析...")
        
        # 分批处理
        batch_size = 10
        for i in range(0, len(sample_df), batch_size):
            batch_end = min(i + batch_size, len(sample_df))
            print(f"处理第 {i+1}-{batch_end} 条记录...")
            
            batch_df = sample_df.iloc[i:batch_end]
            
            for idx, row in batch_df.iterrows():
                product_info = {
                    '商品ID': row['商品ID'],
                    '商品名': row['商品名'],
                    '类目': row['类目'],
                    '商品描述': row.get('商品描述', '')
                }
                
                # 关键词分类
                keyword_result = classifier.classify_single_product(product_info)
                sample_df.at[idx, '关键词预测'] = keyword_result
                
                # AI分类
                try:
                    ai_result = classifier.classify_with_ai(product_info)
                    sample_df.at[idx, 'AI预测'] = ai_result
                except Exception as e:
                    print(f"AI分类失败 (商品ID: {row['商品ID']}): {e}")
                    sample_df.at[idx, 'AI预测'] = keyword_result
                
                # 检查匹配并分析原因
                manual_label = str(row['人工标签'])
                ai_predicted = str(sample_df.at[idx, 'AI预测'])
                keyword_predicted = str(sample_df.at[idx, '关键词预测'])

                if manual_label == ai_predicted:
                    sample_df.at[idx, '匹配情况'] = '匹配'
                    sample_df.at[idx, '不匹配原因'] = ''
                    sample_df.at[idx, '不匹配类型'] = ''
                    sample_df.at[idx, '可能的标记错误'] = '否'
                    sample_df.at[idx, '置信度'] = ''
                else:
                    sample_df.at[idx, '匹配情况'] = '不匹配'

                    # 详细分析不匹配原因
                    reason, mismatch_type, confidence, possible_error = analyze_mismatch_reason(
                        row, keyword_predicted, ai_predicted, manual_label
                    )

                    sample_df.at[idx, '不匹配原因'] = reason
                    sample_df.at[idx, '不匹配类型'] = mismatch_type
                    sample_df.at[idx, '置信度'] = confidence
                    sample_df.at[idx, '可能的标记错误'] = possible_error
        
        # 分析结果
        print(f"\n" + "="*80)
        print("分析结果统计")
        print("="*80)
        
        # 总体统计
        total_count = len(sample_df)
        match_count = (sample_df['匹配情况'] == '匹配').sum()
        mismatch_count = total_count - match_count
        
        print(f"总样本数: {total_count}")
        print(f"匹配数: {match_count} ({match_count/total_count:.1%})")
        print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count:.1%})")
        
        # 不匹配原因统计
        mismatched_data = sample_df[sample_df['匹配情况'] == '不匹配']
        print(f"\n不匹配原因分析:")
        if len(mismatched_data) > 0:
            # 按不匹配类型统计
            mismatch_type_counts = mismatched_data['不匹配类型'].value_counts()
            print(f"不匹配类型分布:")
            for mtype, count in mismatch_type_counts.items():
                print(f"  {mtype}: {count} 条 ({count/len(mismatched_data):.1%})")

            # 按置信度统计
            confidence_counts = mismatched_data['置信度'].value_counts()
            print(f"\n置信度分布:")
            for conf, count in confidence_counts.items():
                print(f"  {conf}: {count} 条 ({count/len(mismatched_data):.1%})")

        # 可能标记错误的统计
        possible_errors = sample_df[sample_df['可能的标记错误'].str.contains('是', na=False)]
        print(f"\n可能的标记错误: {len(possible_errors)} 条")

        if len(possible_errors) > 0:
            print(f"\n可能标记错误的商品 (前10条):")
            error_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '不匹配类型', '置信度']
            print(possible_errors[error_cols].head(10).to_string(index=False))
        
        # 各标签的准确率
        print(f"\n各标签准确率分析:")
        for label in ['全季', 'SS', 'AW']:
            label_data = sample_df[sample_df['人工标签'] == label]
            if len(label_data) > 0:
                label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
                accuracy = label_correct / len(label_data)
                target = 0.95 if label == '全季' else 0.90
                status = "✓ 达成" if accuracy >= target else "✗ 未达成"
                print(f"  {label}: {accuracy:.1%} ({label_correct}/{len(label_data)}) 目标:{target:.0%} {status}")
                
                # 显示该标签下的不匹配情况
                label_mismatches = label_data[label_data['匹配情况'] == '不匹配']
                if len(label_mismatches) > 0:
                    print(f"    不匹配商品: {len(label_mismatches)} 条")
                    ai_predictions = label_mismatches['AI预测'].value_counts()
                    print(f"    AI预测分布: {dict(ai_predictions)}")
        
        # 保存详细分析结果
        output_file = f"季节标签不匹配分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        sample_df.to_excel(output_file, index=False)
        print(f"\n详细分析结果已保存到: {output_file}")
        
        # 保存可能的标记错误
        if len(possible_errors) > 0:
            error_file = f"可能的标记错误_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            error_detail_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '关键词预测',
                                '不匹配原因', '不匹配类型', '置信度', '可能的标记错误']
            possible_errors[error_detail_cols].to_csv(error_file, index=False, encoding='utf-8-sig')
            print(f"可能的标记错误已保存到: {error_file}")

        # 生成不匹配原因分析表格
        if len(mismatched_data) > 0:
            mismatch_analysis_file = f"不匹配原因分析表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            analysis_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '关键词预测',
                           '不匹配原因', '不匹配类型', '置信度', '可能的标记错误']
            mismatched_data[analysis_cols].to_csv(mismatch_analysis_file, index=False, encoding='utf-8-sig')
            print(f"不匹配原因分析表已保存到: {mismatch_analysis_file}")

            # 生成不匹配类型汇总表
            mismatch_summary = mismatched_data.groupby(['不匹配类型', '置信度']).agg({
                '商品ID': 'count',
                '人工标签': lambda x: x.value_counts().to_dict(),
                'AI预测': lambda x: x.value_counts().to_dict()
            }).rename(columns={'商品ID': '数量'})

            summary_file = f"不匹配类型汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            mismatch_summary.to_csv(summary_file, encoding='utf-8-sig')
            print(f"不匹配类型汇总表已保存到: {summary_file}")
        
        # 生成修正建议
        print(f"\n" + "="*80)
        print("修正建议")
        print("="*80)
        
        if len(possible_errors) > 0:
            print(f"发现 {len(possible_errors)} 个可能的原始数据标记错误：")
            for _, row in possible_errors.iterrows():
                print(f"  商品ID {row['商品ID']}: {row['商品名'][:30]}...")
                print(f"    当前标签: {row['人工标签']} -> 建议标签: {row['AI预测']}")
                print(f"    理由: {row['可能的标记错误']}")
                print()
        
        # 如果修正这些错误后的准确率
        corrected_df = sample_df.copy()
        for idx, row in possible_errors.iterrows():
            corrected_df.at[idx, '人工标签'] = row['AI预测']
        
        corrected_accuracy = (corrected_df['人工标签'] == corrected_df['AI预测']).sum() / len(corrected_df)
        print(f"如果修正可能的标记错误，预期准确率: {corrected_accuracy:.1%}")
        
        return sample_df, possible_errors
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("开始大规模分层抽样分析...")
    print("每个标签抽取500条，总计1500条数据")

    sample_df, possible_errors = analyze_sample_data(samples_per_label=500)

    if sample_df is not None:
        print(f"\n" + "="*80)
        print("分析完成！")
        print("="*80)
        print(f"已生成以下分析文件：")
        print(f"1. 详细分析结果 - Excel格式")
        print(f"2. 可能的标记错误 - CSV格式")
        print(f"3. 不匹配原因分析表 - CSV格式")
        print(f"4. 不匹配类型汇总 - CSV格式")
        print(f"\n建议：")
        print(f"1. 重点关注'置信度'为'高'的不匹配项")
        print(f"2. 检查'明显季节性商品误标为全季'类型的商品")
        print(f"3. 考虑修正明显的标记错误以提高系统准确率")
    else:
        print("分析失败，请检查数据文件和配置")

if __name__ == "__main__":
    main()
