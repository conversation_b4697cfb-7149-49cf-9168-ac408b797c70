# 季节标签分类Agent - 部署指南

## 📋 部署概述

本文档提供季节标签分类Agent的完整部署指南，包括环境准备、安装配置、启动运行和监控维护等内容。

**系统版本**: v1.1  
**部署环境**: 生产环境  
**更新时间**: 2025年7月17日  

---

## 🔧 环境要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上（推荐16GB）
- **存储**: 100GB以上可用空间
- **网络**: 稳定的互联网连接（访问Claude API）

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+) / Windows Server 2019+ / macOS 10.15+
- **Python**: 3.8 - 3.11
- **数据库**: PostgreSQL 12+ (可选)
- **缓存**: Redis 6+ (可选)
- **容器**: Docker 20.10+ (推荐)

### 网络要求
- **出站访问**: 需要访问 `api.anthropic.com` (Claude API)
- **端口**: 8080 (API服务), 9090 (监控), 5432 (数据库), 6379 (Redis)

---

## 🚀 快速部署

### 方式一：Docker部署（推荐）

#### 1. 准备Docker环境
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 创建部署目录
```bash
mkdir -p /opt/season-agent
cd /opt/season-agent

# 复制项目文件
cp -r final_delivery/* .
```

#### 3. 配置环境变量
```bash
# 创建环境变量文件
cat > .env << EOF
# API配置
CLAUDE_API_KEY=your_claude_api_key_here
API_BASE_URL=https://api.anthropic.com/v1/messages

# 数据库配置（可选）
DB_HOST=postgres
DB_PORT=5432
DB_NAME=season_agent
DB_USER=agent_user
DB_PASSWORD=secure_password

# Redis配置（可选）
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# 应用配置
APP_PORT=8080
LOG_LEVEL=INFO
WORKERS=4
EOF
```

#### 4. 创建Docker Compose文件
```yaml
# docker-compose.yml
version: '3.8'

services:
  season-agent:
    build: .
    ports:
      - "8080:8080"
    environment:
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - DB_HOST=postgres
      - REDIS_HOST=redis
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

#### 5. 创建Dockerfile
```dockerfile
# Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data/uploads data/results logs

# 设置权限
RUN chmod +x core/*.py

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

#### 6. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f season-agent
```

### 方式二：传统部署

#### 1. 环境准备
```bash
# 创建用户
sudo useradd -m -s /bin/bash season-agent
sudo usermod -aG sudo season-agent

# 切换用户
sudo su - season-agent

# 创建应用目录
mkdir -p /opt/season-agent
cd /opt/season-agent
```

#### 2. Python环境配置
```bash
# 安装Python 3.9
sudo apt update
sudo apt install python3.9 python3.9-venv python3.9-dev

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

#### 3. 安装应用
```bash
# 复制应用文件
cp -r final_delivery/* .

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export CLAUDE_API_KEY="your_api_key_here"
export LOG_LEVEL="INFO"
```

#### 4. 配置系统服务
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/season-agent.service > /dev/null <<EOF
[Unit]
Description=Season Label Classification Agent
After=network.target

[Service]
Type=simple
User=season-agent
WorkingDirectory=/opt/season-agent
Environment=PATH=/opt/season-agent/venv/bin
Environment=CLAUDE_API_KEY=your_api_key_here
ExecStart=/opt/season-agent/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8080
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable season-agent
sudo systemctl start season-agent

# 检查服务状态
sudo systemctl status season-agent
```

---

## ⚙️ 配置说明

### 主配置文件 (config/config.yaml)
```yaml
# API配置
api:
  claude_api_key: "${CLAUDE_API_KEY}"
  timeout: 30
  max_retries: 3

# 处理配置
processing:
  batch_size: 50          # 生产环境建议50
  max_concurrent: 10      # 根据服务器性能调整
  temp_dir: "./temp"
  output_dir: "./output"

# 质量控制
quality:
  accuracy_threshold: 0.95
  confidence_threshold: 0.85
  error_alert_threshold: 0.05

# 性能配置
performance:
  enable_cache: true
  cache_ttl: 3600
  enable_async: true
  request_timeout: 30
```

### 数据库初始化 (init.sql)
```sql
-- 创建数据库和用户
CREATE DATABASE season_agent;
CREATE USER agent_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE season_agent TO agent_user;

-- 切换到应用数据库
\c season_agent;

-- 创建表结构
CREATE TABLE classification_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(50) UNIQUE NOT NULL,
    filename VARCHAR(255),
    total_count INTEGER,
    processed_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'processing',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    result_file_path VARCHAR(500)
);

CREATE TABLE classification_results (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(50) REFERENCES classification_tasks(task_id),
    product_id VARCHAR(100),
    product_name TEXT,
    category VARCHAR(200),
    manual_label VARCHAR(10),
    keyword_prediction VARCHAR(10),
    ai_prediction VARCHAR(10),
    final_label VARCHAR(10),
    confidence DECIMAL(4,3),
    reasoning TEXT,
    is_match BOOLEAN,
    processing_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_classification_results_task_id ON classification_results(task_id);
CREATE INDEX idx_classification_results_product_id ON classification_results(product_id);
```

---

## 📊 监控配置

### Prometheus配置 (monitoring/prometheus.yml)
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'season-agent'
    static_configs:
      - targets: ['season-agent:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 告警规则 (monitoring/alert_rules.yml)
```yaml
groups:
  - name: season_agent_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(classification_errors_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "分类错误率过高"
          description: "过去5分钟分类错误率超过5%"

      - alert: LowAccuracy
        expr: classification_accuracy < 0.90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "分类准确率过低"
          description: "分类准确率低于90%"

      - alert: ServiceDown
        expr: up{job="season-agent"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务不可用"
          description: "季节标签分类服务已停止"
```

---

## 🔍 验证部署

### 1. 健康检查
```bash
# 检查服务状态
curl http://localhost:8080/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2025-07-17T16:21:10Z",
  "version": "v1.1",
  "components": {
    "classifier": "healthy",
    "ai_client": "healthy"
  }
}
```

### 2. 功能测试
```bash
# 测试单个分类
curl -X POST "http://localhost:8080/api/v1/classify/single" \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "男士短袖T恤",
    "category": "服装"
  }'

# 预期响应
{
  "code": 200,
  "message": "success",
  "data": {
    "final_label": "SS",
    "confidence": 0.95
  }
}
```

### 3. 性能测试
```bash
# 使用ab进行压力测试
ab -n 1000 -c 10 -H "Content-Type: application/json" \
   -p test_data.json http://localhost:8080/api/v1/classify/single

# 监控响应时间和成功率
```

---

## 🛠️ 运维管理

### 日志管理
```bash
# 查看应用日志
tail -f logs/season_agent.log

# 查看Docker日志
docker-compose logs -f season-agent

# 日志轮转配置
sudo tee /etc/logrotate.d/season-agent > /dev/null <<EOF
/opt/season-agent/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 season-agent season-agent
}
EOF
```

### 备份策略
```bash
# 数据备份脚本
#!/bin/bash
BACKUP_DIR="/backup/season-agent"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份数据库
pg_dump -h localhost -U agent_user season_agent > $BACKUP_DIR/db_$DATE.sql

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz config/

# 备份日志
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/

# 清理30天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 更新部署
```bash
# 停止服务
docker-compose down

# 备份当前版本
cp -r . ../season-agent-backup-$(date +%Y%m%d)

# 更新代码
git pull origin main

# 重新构建和启动
docker-compose build
docker-compose up -d

# 验证更新
curl http://localhost:8080/health
```

---

## ⚠️ 故障排除

### 常见问题

1. **API调用失败**
   ```bash
   # 检查API密钥
   echo $CLAUDE_API_KEY
   
   # 测试网络连接
   curl -I https://api.anthropic.com
   
   # 检查日志
   grep "API" logs/season_agent.log
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 调整批处理大小
   # 在config.yaml中减小batch_size
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose ps postgres
   
   # 测试连接
   psql -h localhost -U agent_user -d season_agent
   ```

### 性能优化
```bash
# 调整系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 调整Docker资源限制
# 在docker-compose.yml中添加
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2'
```

---

## 📞 技术支持

**部署支持**: <EMAIL>  
**运维支持**: <EMAIL>  
**紧急联系**: <EMAIL>  

**文档版本**: v1.1  
**最后更新**: 2025年7月17日  
**下次审核**: 2025年10月17日
