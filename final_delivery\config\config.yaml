# 季节标签分类Agent配置文件

# API配置
api:
  claude_api_key: "${CLAUDE_API_KEY}"  # 从环境变量获取
  base_url: "https://api.anthropic.com/v1/messages"
  model: "claude-3-sonnet-20240229"
  timeout: 30
  max_retries: 3

# 处理配置
processing:
  batch_size: 20
  max_concurrent: 5
  temp_dir: "./temp"
  output_dir: "./output"

# 质量控制
quality:
  accuracy_threshold: 0.90
  confidence_threshold: 0.80
  error_alert_threshold: 0.05
  min_confidence_for_auto: 0.75

# 数据库配置（可选）
database:
  host: "localhost"
  port: 5432
  name: "season_agent"
  user: "agent_user"
  password: "${DB_PASSWORD}"
  pool_size: 10
  max_overflow: 20

# Redis配置（可选）
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: "${REDIS_PASSWORD}"
  max_connections: 10

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/season_agent.log"
  max_size: "10MB"
  backup_count: 5

# 监控配置
monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30
  alert_webhook: "${ALERT_WEBHOOK_URL}"

# 分类规则配置
classification:
  rules_file: "./config/rules.json"
  auto_update_rules: true
  rule_update_interval: 3600  # 秒

# 性能配置
performance:
  enable_cache: true
  cache_ttl: 3600  # 秒
  enable_async: true
  request_timeout: 30
