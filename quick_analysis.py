#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速分析 - 每个标签50条，总计150条
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime
import time

def analyze_mismatch_simple(row, keyword_predicted, ai_predicted, manual_label):
    """简化的不匹配原因分析"""
    product_name = str(row['商品名']).lower()
    
    # 强季节性关键词
    aw_keywords = ['羽绒', '冬季', '保暖', '加厚', '防风', '毛呢', '皮草', '雪地靴', '棉拖']
    ss_keywords = ['夏季', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气']
    
    # 检查关键词
    has_aw = any(kw in product_name for kw in aw_keywords)
    has_ss = any(kw in product_name for kw in ss_keywords)
    
    reason = ""
    mismatch_type = ""
    confidence = "低"
    possible_error = "否"
    
    if manual_label == '全季' and ai_predicted in ['AW', 'SS']:
        if has_aw and ai_predicted == 'AW':
            reason = f"商品名包含AW关键词，AI判断为AW，但人工标记为全季"
            mismatch_type = "明显AW商品误标为全季"
            confidence = "高"
            possible_error = "是"
        elif has_ss and ai_predicted == 'SS':
            reason = f"商品名包含SS关键词，AI判断为SS，但人工标记为全季"
            mismatch_type = "明显SS商品误标为全季"
            confidence = "高"
            possible_error = "是"
        else:
            reason = f"AI判断为{ai_predicted}，与人工标记全季不一致"
            mismatch_type = f"{ai_predicted}判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label in ['AW', 'SS'] and ai_predicted == '全季':
        reason = f"AI判断为全季，但人工标记为{manual_label}"
        mismatch_type = f"全季商品误标为{manual_label}"
        confidence = "中"
        possible_error = "可能"
    
    elif manual_label != ai_predicted and manual_label in ['AW', 'SS'] and ai_predicted in ['AW', 'SS']:
        if (has_aw and ai_predicted == 'AW') or (has_ss and ai_predicted == 'SS'):
            reason = f"商品名特征支持AI判断{ai_predicted}，但人工标记为{manual_label}"
            mismatch_type = "季节标签错误"
            confidence = "高"
            possible_error = "是"
        else:
            reason = f"AI判断{ai_predicted}与人工标记{manual_label}不一致"
            mismatch_type = "季节判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    return reason, mismatch_type, confidence, possible_error

def main():
    """主函数"""
    print("="*80)
    print("快速分析 - 分层抽样150条")
    print("每个标签50条，总计150条数据")
    print("="*80)
    
    try:
        # 读取数据
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")
        
        # 重命名列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })
        
        # 查看标签分布
        label_counts = df['人工标签'].value_counts()
        print(f"\n原始数据标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} 条")
        
        # 分层抽样
        sample_dfs = []
        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) >= 50:
                label_sample = label_data.sample(n=50, random_state=42)
                sample_dfs.append(label_sample)
                print(f"  {label}: 抽样 50 条")
            else:
                sample_dfs.append(label_data)
                print(f"  {label}: 全部 {len(label_data)} 条")
        
        # 合并样本
        sample_df = pd.concat(sample_dfs, ignore_index=True)
        sample_df = sample_df.sample(frac=1, random_state=42).reset_index(drop=True)
        print(f"\n总抽样数据: {len(sample_df)} 条")
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 添加分析列
        sample_df['关键词预测'] = ''
        sample_df['AI预测'] = ''
        sample_df['匹配情况'] = ''
        sample_df['不匹配原因'] = ''
        sample_df['不匹配类型'] = ''
        sample_df['可能的标记错误'] = ''
        sample_df['置信度'] = ''
        
        print(f"\n开始处理...")
        start_time = time.time()
        
        # 逐个处理
        for idx, row in sample_df.iterrows():
            product_info = {
                '商品ID': row['商品ID'],
                '商品名': row['商品名'],
                '类目': row['类目'],
                '商品描述': row.get('商品描述', '')
            }
            
            try:
                # 关键词分类
                keyword_result = classifier.classify_single_product(product_info)
                sample_df.at[idx, '关键词预测'] = keyword_result
                
                # AI分类
                ai_result = classifier.classify_with_ai(product_info)
                sample_df.at[idx, 'AI预测'] = ai_result
                
                # 分析匹配情况
                manual_label = str(row['人工标签'])
                
                if manual_label == ai_result:
                    sample_df.at[idx, '匹配情况'] = '匹配'
                    sample_df.at[idx, '不匹配原因'] = ''
                    sample_df.at[idx, '不匹配类型'] = ''
                    sample_df.at[idx, '可能的标记错误'] = '否'
                    sample_df.at[idx, '置信度'] = ''
                else:
                    sample_df.at[idx, '匹配情况'] = '不匹配'
                    
                    # 分析不匹配原因
                    reason, mismatch_type, confidence, possible_error = analyze_mismatch_simple(
                        row, keyword_result, ai_result, manual_label
                    )
                    
                    sample_df.at[idx, '不匹配原因'] = reason
                    sample_df.at[idx, '不匹配类型'] = mismatch_type
                    sample_df.at[idx, '置信度'] = confidence
                    sample_df.at[idx, '可能的标记错误'] = possible_error
                
                # 显示进度
                if (idx + 1) % 10 == 0:
                    elapsed_time = time.time() - start_time
                    avg_time = elapsed_time / (idx + 1)
                    remaining_time = avg_time * (len(sample_df) - idx - 1)
                    print(f"  已处理: {idx+1}/{len(sample_df)} "
                          f"({(idx+1)/len(sample_df):.1%}) "
                          f"预计剩余: {remaining_time/60:.1f}分钟")
                
            except Exception as e:
                print(f"  处理失败 (第{idx+1}条): {e}")
                sample_df.at[idx, 'AI预测'] = keyword_result if 'keyword_result' in locals() else '全季'
                sample_df.at[idx, '不匹配原因'] = f"处理失败: {str(e)}"
        
        # 生成分析报告
        print(f"\n" + "="*80)
        print("分析结果")
        print("="*80)
        
        # 基本统计
        total_count = len(sample_df)
        match_count = (sample_df['匹配情况'] == '匹配').sum()
        mismatch_count = total_count - match_count
        
        print(f"总样本数: {total_count}")
        print(f"匹配数: {match_count} ({match_count/total_count:.1%})")
        print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count:.1%})")
        
        # 各标签准确率
        print(f"\n各标签准确率:")
        for label in ['全季', 'SS', 'AW']:
            label_data = sample_df[sample_df['人工标签'] == label]
            if len(label_data) > 0:
                label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
                accuracy = label_correct / len(label_data)
                target = 0.95 if label == '全季' else 0.90
                status = "✓ 达成" if accuracy >= target else "✗ 未达成"
                print(f"  {label}: {accuracy:.1%} ({label_correct}/{len(label_data)}) 目标:{target:.0%} {status}")
        
        # 不匹配分析
        mismatched_data = sample_df[sample_df['匹配情况'] == '不匹配']
        if len(mismatched_data) > 0:
            print(f"\n不匹配类型分布:")
            mismatch_types = mismatched_data['不匹配类型'].value_counts()
            for mtype, count in mismatch_types.items():
                print(f"  {mtype}: {count} 条 ({count/len(mismatched_data):.1%})")
            
            print(f"\n置信度分布:")
            confidence_dist = mismatched_data['置信度'].value_counts()
            for conf, count in confidence_dist.items():
                print(f"  {conf}: {count} 条 ({count/len(mismatched_data):.1%})")
        
        # 可能的标记错误
        possible_errors = sample_df[sample_df['可能的标记错误'] == '是']
        print(f"\n可能的标记错误: {len(possible_errors)} 条 ({len(possible_errors)/total_count:.1%})")
        
        if len(possible_errors) > 0:
            print(f"\n可能标记错误的商品:")
            error_cols = ['商品ID', '商品名', '人工标签', 'AI预测', '不匹配类型', '置信度']
            print(possible_errors[error_cols].to_string(index=False))
        
        # 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 完整结果
        full_result_file = f"快速分析结果_{timestamp}.xlsx"
        sample_df.to_excel(full_result_file, index=False)
        print(f"\n完整结果已保存到: {full_result_file}")
        
        # 不匹配详情
        if len(mismatched_data) > 0:
            mismatch_file = f"不匹配详情_{timestamp}.csv"
            analysis_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '关键词预测',
                            '不匹配原因', '不匹配类型', '置信度', '可能的标记错误']
            mismatched_data[analysis_cols].to_csv(mismatch_file, index=False, encoding='utf-8-sig')
            print(f"不匹配详情已保存到: {mismatch_file}")
        
        # 可能的标记错误
        if len(possible_errors) > 0:
            error_file = f"可能的标记错误_{timestamp}.csv"
            possible_errors[analysis_cols].to_csv(error_file, index=False, encoding='utf-8-sig')
            print(f"可能的标记错误已保存到: {error_file}")
        
        print(f"\n" + "="*80)
        print("分析完成！")
        print("="*80)
        print(f"主要发现:")
        print(f"1. 总体准确率: {match_count/total_count:.1%}")
        print(f"2. 可能的标记错误: {len(possible_errors)} 条")
        print(f"3. 建议重点关注'置信度'为'高'的不匹配项")
        
        total_time = time.time() - start_time
        print(f"4. 总处理时间: {total_time/60:.1f}分钟")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
