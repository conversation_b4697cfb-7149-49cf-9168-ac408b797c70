#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模分层抽样分析 - 每个标签500条，总计1500条
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime
import time

def analyze_detailed_mismatch(row, keyword_predicted, ai_predicted, manual_label):
    """详细分析不匹配原因"""
    product_name = str(row['商品名']).lower()
    category = str(row['类目']).lower()
    
    # 强季节性关键词
    strong_aw_keywords = ['羽绒', '冬季', '保暖', '加厚', '防风', '毛呢', '皮草', '雪地靴', '棉拖', '冲锋衣']
    strong_ss_keywords = ['夏季', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气', '短裤', '短裙']
    
    # 检查关键词
    aw_keywords_found = [kw for kw in strong_aw_keywords if kw in product_name]
    ss_keywords_found = [kw for kw in strong_ss_keywords if kw in product_name]
    
    reason = ""
    mismatch_type = ""
    confidence = "低"
    possible_error = "否"
    
    # 详细分析各种情况
    if manual_label == '全季' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含明显AW关键词{aw_keywords_found}，AI判断为AW，但人工标记为全季"
            mismatch_type = "明显AW商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显AW特征"
        else:
            reason = f"AI基于商品特征判断为AW，可能考虑了类目等因素"
            mismatch_type = "AW判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == '全季' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含明显SS关键词{ss_keywords_found}，AI判断为SS，但人工标记为全季"
            mismatch_type = "明显SS商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显SS特征"
        else:
            reason = f"AI基于商品特征判断为SS，可能考虑了类目等因素"
            mismatch_type = "SS判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label in ['AW', 'SS'] and ai_predicted == '全季':
        all_season_categories = ['美妆个护', '食品', '电器', '居家日用', '母婴', '宠物', '箱包']
        is_all_season_category = any(cat in category for cat in all_season_categories)
        
        if is_all_season_category and not (aw_keywords_found or ss_keywords_found):
            reason = f"商品属于全季类目({category})且无明显季节性特征，AI判断为全季，但人工标记为{manual_label}"
            mismatch_type = f"全季商品误标为{manual_label}"
            confidence = "中"
            possible_error = "可能"
        else:
            reason = f"AI判断为全季，但人工标记为{manual_label}"
            mismatch_type = f"{manual_label}判断分歧"
            confidence = "低"
            possible_error = "否"
    
    elif manual_label == 'AW' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含SS关键词{ss_keywords_found}，AI判断为SS，但人工标记为AW"
            mismatch_type = "季节标签错误(AW->SS)"
            confidence = "高"
            possible_error = "是 - 季节标签错误"
        else:
            reason = f"AI判断为SS与人工标记AW不一致"
            mismatch_type = "季节判断分歧(AW vs SS)"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == 'SS' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含AW关键词{aw_keywords_found}，AI判断为AW，但人工标记为SS"
            mismatch_type = "季节标签错误(SS->AW)"
            confidence = "高"
            possible_error = "是 - 季节标签错误"
        else:
            reason = f"AI判断为AW与人工标记SS不一致"
            mismatch_type = "季节判断分歧(SS vs AW)"
            confidence = "中"
            possible_error = "可能"
    
    # 如果关键词预测和AI预测一致，提高置信度
    if keyword_predicted == ai_predicted and keyword_predicted != manual_label:
        if confidence == "中":
            confidence = "高"
        elif confidence == "低":
            confidence = "中"
        if possible_error == "否":
            possible_error = "可能"
    
    return reason, mismatch_type, confidence, possible_error

def process_batch(sample_df, classifier, batch_size=20):
    """批量处理数据"""
    total_count = len(sample_df)
    processed_count = 0
    
    # 添加分析列
    sample_df['关键词预测'] = ''
    sample_df['AI预测'] = ''
    sample_df['匹配情况'] = ''
    sample_df['不匹配原因'] = ''
    sample_df['不匹配类型'] = ''
    sample_df['可能的标记错误'] = ''
    sample_df['置信度'] = ''
    
    print(f"开始批量处理 {total_count} 条数据，批次大小: {batch_size}")
    start_time = time.time()
    
    for i in range(0, total_count, batch_size):
        batch_end = min(i + batch_size, total_count)
        print(f"\n处理批次 {i//batch_size + 1}: 第 {i+1}-{batch_end} 条记录")
        
        for idx in range(i, batch_end):
            row = sample_df.iloc[idx]
            product_info = {
                '商品ID': row['商品ID'],
                '商品名': row['商品名'],
                '类目': row['类目'],
                '商品描述': row.get('商品描述', '')
            }
            
            try:
                # 关键词分类
                keyword_result = classifier.classify_single_product(product_info)
                sample_df.at[sample_df.index[idx], '关键词预测'] = keyword_result
                
                # AI分类
                ai_result = classifier.classify_with_ai(product_info)
                sample_df.at[sample_df.index[idx], 'AI预测'] = ai_result
                
                # 分析匹配情况
                manual_label = str(row['人工标签'])
                
                if manual_label == ai_result:
                    sample_df.at[sample_df.index[idx], '匹配情况'] = '匹配'
                    sample_df.at[sample_df.index[idx], '不匹配原因'] = ''
                    sample_df.at[sample_df.index[idx], '不匹配类型'] = ''
                    sample_df.at[sample_df.index[idx], '可能的标记错误'] = '否'
                    sample_df.at[sample_df.index[idx], '置信度'] = ''
                else:
                    sample_df.at[sample_df.index[idx], '匹配情况'] = '不匹配'
                    
                    # 分析不匹配原因
                    reason, mismatch_type, confidence, possible_error = analyze_detailed_mismatch(
                        row, keyword_result, ai_result, manual_label
                    )
                    
                    sample_df.at[sample_df.index[idx], '不匹配原因'] = reason
                    sample_df.at[sample_df.index[idx], '不匹配类型'] = mismatch_type
                    sample_df.at[sample_df.index[idx], '置信度'] = confidence
                    sample_df.at[sample_df.index[idx], '可能的标记错误'] = possible_error
                
                processed_count += 1
                
                # 显示进度
                if processed_count % 50 == 0:
                    elapsed_time = time.time() - start_time
                    avg_time_per_item = elapsed_time / processed_count
                    remaining_items = total_count - processed_count
                    estimated_remaining_time = remaining_items * avg_time_per_item
                    
                    print(f"  已处理: {processed_count}/{total_count} "
                          f"({processed_count/total_count:.1%}) "
                          f"预计剩余时间: {estimated_remaining_time/60:.1f}分钟")
                
            except Exception as e:
                print(f"  处理失败 (第{idx+1}条): {e}")
                sample_df.at[sample_df.index[idx], 'AI预测'] = keyword_result if 'keyword_result' in locals() else '全季'
                sample_df.at[sample_df.index[idx], '不匹配原因'] = f"处理失败: {str(e)}"
        
        # 保存中间结果
        if (i + batch_size) % 200 == 0 or batch_end == total_count:
            temp_file = f"temp_results_{processed_count}.xlsx"
            sample_df.to_excel(temp_file, index=False)
            print(f"  中间结果已保存: {temp_file}")
    
    total_time = time.time() - start_time
    print(f"\n批量处理完成！总耗时: {total_time/60:.1f}分钟")
    
    return sample_df

def generate_detailed_report(df):
    """生成详细分析报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"\n" + "="*80)
    print("详细分析报告")
    print("="*80)
    
    # 基本统计
    total_count = len(df)
    match_count = (df['匹配情况'] == '匹配').sum()
    mismatch_count = total_count - match_count
    
    print(f"总样本数: {total_count}")
    print(f"匹配数: {match_count} ({match_count/total_count:.1%})")
    print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count:.1%})")
    
    # 各标签准确率
    print(f"\n各标签准确率:")
    accuracy_summary = []
    for label in ['全季', 'SS', 'AW']:
        label_data = df[df['人工标签'] == label]
        if len(label_data) > 0:
            label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
            accuracy = label_correct / len(label_data)
            target = 0.95 if label == '全季' else 0.90
            status = "✓ 达成" if accuracy >= target else "✗ 未达成"
            print(f"  {label}: {accuracy:.1%} ({label_correct}/{len(label_data)}) 目标:{target:.0%} {status}")
            
            accuracy_summary.append({
                '标签': label,
                '样本数': len(label_data),
                '正确数': label_correct,
                '准确率': accuracy,
                '目标准确率': target,
                '是否达成': status
            })
    
    # 不匹配分析
    mismatched_data = df[df['匹配情况'] == '不匹配']
    if len(mismatched_data) > 0:
        print(f"\n不匹配类型分布:")
        mismatch_types = mismatched_data['不匹配类型'].value_counts()
        for mtype, count in mismatch_types.items():
            print(f"  {mtype}: {count} 条 ({count/len(mismatched_data):.1%})")
        
        print(f"\n置信度分布:")
        confidence_dist = mismatched_data['置信度'].value_counts()
        for conf, count in confidence_dist.items():
            print(f"  {conf}: {count} 条 ({count/len(mismatched_data):.1%})")
        
        # 可能的标记错误
        possible_errors = df[df['可能的标记错误'].str.contains('是', na=False)]
        print(f"\n可能的标记错误: {len(possible_errors)} 条 ({len(possible_errors)/total_count:.1%})")
        
        # 按原始标签分析可能的错误
        print(f"\n各标签可能错误分布:")
        for label in ['全季', 'SS', 'AW']:
            label_errors = possible_errors[possible_errors['人工标签'] == label]
            if len(label_errors) > 0:
                print(f"  {label}: {len(label_errors)} 条可能错误")
                ai_predictions = label_errors['AI预测'].value_counts()
                for ai_label, count in ai_predictions.items():
                    print(f"    -> {ai_label}: {count} 条")
    
    # 保存详细文件
    print(f"\n保存分析文件...")
    
    # 1. 完整结果
    full_result_file = f"季节标签大规模分析_{timestamp}.xlsx"
    df.to_excel(full_result_file, index=False)
    print(f"1. 完整分析结果: {full_result_file}")
    
    # 2. 不匹配详情
    if len(mismatched_data) > 0:
        mismatch_file = f"不匹配详情分析_{timestamp}.csv"
        analysis_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '关键词预测',
                        '不匹配原因', '不匹配类型', '置信度', '可能的标记错误']
        mismatched_data[analysis_cols].to_csv(mismatch_file, index=False, encoding='utf-8-sig')
        print(f"2. 不匹配详情分析: {mismatch_file}")
    
    # 3. 可能的标记错误
    possible_errors = df[df['可能的标记错误'].str.contains('是', na=False)]
    if len(possible_errors) > 0:
        error_file = f"可能的标记错误_{timestamp}.csv"
        possible_errors[analysis_cols].to_csv(error_file, index=False, encoding='utf-8-sig')
        print(f"3. 可能的标记错误: {error_file}")
    
    # 4. 准确率汇总
    accuracy_df = pd.DataFrame(accuracy_summary)
    accuracy_file = f"准确率汇总_{timestamp}.csv"
    accuracy_df.to_csv(accuracy_file, index=False, encoding='utf-8-sig')
    print(f"4. 准确率汇总: {accuracy_file}")
    
    # 5. 不匹配类型汇总
    if len(mismatched_data) > 0:
        mismatch_summary = mismatched_data.groupby(['不匹配类型', '置信度']).size().reset_index(name='数量')
        summary_file = f"不匹配类型汇总_{timestamp}.csv"
        mismatch_summary.to_csv(summary_file, index=False, encoding='utf-8-sig')
        print(f"5. 不匹配类型汇总: {summary_file}")
    
    return accuracy_summary, possible_errors

def main():
    """主函数"""
    print("="*80)
    print("中等规模季节标签分析 - 分层抽样300条")
    print("每个标签100条，总计300条数据")
    print("="*80)
    
    try:
        # 读取数据
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")
        
        # 重命名列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })
        
        # 查看标签分布
        label_counts = df['人工标签'].value_counts()
        print(f"\n原始数据标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} 条")
        
        # 分层抽样 - 先用100条测试
        sample_dfs = []
        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) >= 100:
                label_sample = label_data.sample(n=100, random_state=42)
                sample_dfs.append(label_sample)
                print(f"  {label}: 抽样 100 条")
            else:
                sample_dfs.append(label_data)
                print(f"  {label}: 全部 {len(label_data)} 条")
        
        # 合并样本
        sample_df = pd.concat(sample_dfs, ignore_index=True)
        sample_df = sample_df.sample(frac=1, random_state=42).reset_index(drop=True)
        print(f"\n总抽样数据: {len(sample_df)} 条")
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 批量处理
        result_df = process_batch(sample_df, classifier, batch_size=20)
        
        # 生成详细报告
        accuracy_summary, possible_errors = generate_detailed_report(result_df)
        
        print(f"\n" + "="*80)
        print("分析完成！")
        print("="*80)
        print(f"主要发现:")
        print(f"1. 总体准确率: {(result_df['匹配情况'] == '匹配').sum() / len(result_df):.1%}")
        print(f"2. 可能的标记错误: {len(possible_errors)} 条")
        print(f"3. 建议重点关注'置信度'为'高'的不匹配项")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
