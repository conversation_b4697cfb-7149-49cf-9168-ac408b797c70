# 季节标签分类系统

基于大模型的商品季节标签自动分类系统，支持对商品进行季节性判断，输出三种标签：全季、SS（春夏）、AW（秋冬）。

## 功能特点

- **智能分类**：结合关键词匹配和大模型推理
- **高准确率目标**：全季95%，SS90%，AW90%
- **多层判断逻辑**：类目 → 商品名 → 商品描述
- **结果对比**：与人工标签对比，输出不匹配商品分析
- **批量处理**：支持Excel文件批量处理

## 文件结构

```
├── season_label_classifier.py    # 核心分类器
├── test_classifier.py           # 测试脚本
├── process_excel_data.py        # Excel数据处理
├── run_season_classification.py # 主运行脚本
├── prompts/prompt               # 提示词文件
├── 上架商品季节标签.xlsx         # 输入数据文件
└── README.md                    # 说明文档
```

## 安装依赖

```bash
pip install pandas openpyxl openai numpy
```

## 使用方法

### 1. 快速开始

运行主脚本：
```bash
python run_season_classification.py
```

选择运行模式：
- **测试模式**：使用内置测试数据验证系统
- **样本模式**：处理50条样本数据
- **完整模式**：处理全部数据
- **检查模式**：查看Excel文件结构

### 2. 单独使用分类器

```python
from season_label_classifier import SeasonLabelClassifier

# 初始化
classifier = SeasonLabelClassifier(API_KEY, BASE_URL)

# 分类单个商品
product_info = {
    '商品名': '男士羽绒服',
    '类目': '男装/外套',
    '商品描述': '冬季保暖外套'
}

# 关键词分类
keyword_result = classifier.classify_single_product(product_info)

# AI分类
ai_result = classifier.classify_with_ai(product_info)
```

### 3. 处理Excel文件

```python
from season_label_classifier import SeasonLabelClassifier

classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
df_result = classifier.process_excel_file("上架商品季节标签.xlsx")

# 计算准确率
accuracy_stats = classifier.calculate_accuracy(df_result)

# 找出不匹配商品
mismatched = classifier.find_mismatched_items(df_result)
```

## 分类逻辑

### 步骤1：类目检查
- **全季类目**：食品饮料、母婴、家具家装、电器、美妆个护等
- **AW类目**：羽绒服、毛呢、冲锋衣、保暖、靴子等
- **SS类目**：短袖、凉鞋、泳装、防晒等

### 步骤2：商品名检查
- **AW关键词**：羽绒、冬、保暖、加厚、防风、毛呢、皮草等
- **SS关键词**：夏、短袖、清凉、防晒、泳装、凉鞋、透气等

### 步骤3：商品描述检查
- 作为补充判断，使用相同的关键词列表

### 优先级
类目 > 商品名 > 商品描述

## 输出文件

运行后会生成以下文件：

1. **季节标签分类结果_[时间].xlsx**：完整分类结果
2. **商品季节标签_[时间].csv**：简化的商品ID和标签对应表
3. **不匹配商品_[时间].csv**：与人工标签不匹配的商品分析

## 准确率分析

系统会自动计算：
- 总体准确率
- 各标签准确率（全季、SS、AW）
- 样本数分布
- 目标达成情况

## 配置说明

### API配置
在代码中修改以下配置：
```python
API_KEY = "your_api_key"
BASE_URL = "your_base_url"
```

### Excel文件格式
支持的列名（系统会自动识别）：
- 商品ID/ID
- 商品名/商品名称/名称
- 类目/物理类目/分类
- 商品描述/描述/详情
- 人工标签/季节标签/标签

## 注意事项

1. **API限制**：注意API调用频率限制，系统已内置延时
2. **数据质量**：确保Excel文件格式正确，包含必要的商品信息
3. **内存使用**：大文件处理时注意内存使用情况
4. **网络连接**：需要稳定的网络连接访问大模型API

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径和格式
   - 确保文件未被其他程序占用

2. **API调用失败**
   - 检查API密钥和URL配置
   - 确认网络连接正常

3. **准确率不达标**
   - 检查提示词是否合适
   - 调整关键词列表
   - 增加训练样本

### 调试模式

运行测试脚本进行调试：
```bash
python test_classifier.py
```

## 扩展功能

### 自定义关键词
修改 `SeasonLabelClassifier` 类中的关键词列表：
```python
self.aw_keywords = ['羽绒', '冬', '保暖', ...]  # 添加AW关键词
self.ss_keywords = ['夏', '短袖', '清凉', ...]  # 添加SS关键词
```

### 自定义提示词
修改 `prompts/prompt` 文件内容来调整AI判断逻辑。

### 批量大小调整
在处理大文件时，可以调整批量大小：
```python
df_result = run_classification(df, batch_size=20)  # 调整批量大小
```

## 版本信息

- 版本：1.0.0
- 更新时间：2024年
- 支持的Python版本：3.7+

## 联系方式

如有问题或建议，请联系开发团队。
