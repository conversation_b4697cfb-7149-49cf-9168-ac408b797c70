#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试季节标签分类器
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier

def create_test_data():
    """创建测试数据"""
    test_data = [
        {
            '商品ID': 'TEST001',
            '商品名': '男士羽绒服加厚保暖外套',
            '类目': '男装/羽绒服',
            '商品描述': '冬季保暖，防风防寒，加厚设计',
            '人工标签': 'AW'
        },
        {
            '商品ID': 'TEST002', 
            '商品名': '女士短袖T恤夏季清凉',
            '类目': '女装/短袖',
            '商品描述': '夏季透气，清凉舒适，防晒面料',
            '人工标签': 'SS'
        },
        {
            '商品ID': 'TEST003',
            '商品名': '苹果手机保护壳',
            '类目': '数码配件/手机壳',
            '商品描述': '高品质材料，全面保护手机',
            '人工标签': '全季'
        },
        {
            '商品ID': 'TEST004',
            '商品名': '冬季雪地靴女',
            '类目': '女鞋/靴子',
            '商品描述': '防滑保暖，适合雪地行走',
            '人工标签': 'AW'
        },
        {
            '商品ID': 'TEST005',
            '商品名': '防晒霜SPF50+',
            '类目': '美妆个护/防晒',
            '商品描述': '夏季必备，有效防紫外线',
            '人工标签': 'SS'
        }
    ]
    
    return pd.DataFrame(test_data)

def test_classifier():
    """测试分类器"""
    # API配置
    API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    
    # 创建分类器
    classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
    
    # 创建测试数据
    test_df = create_test_data()
    print("测试数据:")
    print(test_df[['商品ID', '商品名', '人工标签']].to_string(index=False))
    print("\n" + "="*50)
    
    # 测试关键词分类
    print("\n测试关键词分类:")
    test_df['关键词预测'] = ''
    for idx, row in test_df.iterrows():
        product_info = row.to_dict()
        keyword_result = classifier.classify_single_product(product_info)
        test_df.at[idx, '关键词预测'] = keyword_result
        print(f"{row['商品ID']}: {row['商品名']} -> {keyword_result}")
    
    # 测试AI分类
    print("\n测试AI分类:")
    test_df['AI预测'] = ''
    for idx, row in test_df.iterrows():
        product_info = row.to_dict()
        ai_result = classifier.classify_with_ai(product_info)
        test_df.at[idx, 'AI预测'] = ai_result
        print(f"{row['商品ID']}: {row['商品名']} -> {ai_result}")
    
    # 计算准确率
    print("\n" + "="*50)
    print("准确率对比:")
    
    # 关键词准确率
    keyword_correct = (test_df['人工标签'] == test_df['关键词预测']).sum()
    keyword_accuracy = keyword_correct / len(test_df)
    print(f"关键词分类准确率: {keyword_accuracy:.2%} ({keyword_correct}/{len(test_df)})")
    
    # AI准确率
    ai_correct = (test_df['人工标签'] == test_df['AI预测']).sum()
    ai_accuracy = ai_correct / len(test_df)
    print(f"AI分类准确率: {ai_accuracy:.2%} ({ai_correct}/{len(test_df)})")
    
    # 显示详细对比
    print("\n详细对比:")
    comparison_df = test_df[['商品ID', '商品名', '人工标签', '关键词预测', 'AI预测']].copy()
    comparison_df['关键词匹配'] = comparison_df['人工标签'] == comparison_df['关键词预测']
    comparison_df['AI匹配'] = comparison_df['人工标签'] == comparison_df['AI预测']
    
    print(comparison_df.to_string(index=False))
    
    return test_df

if __name__ == "__main__":
    test_classifier()
