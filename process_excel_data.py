#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理Excel数据并进行季节标签分类
"""

import pandas as pd
import numpy as np
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime
import os

def check_excel_structure(file_path: str):
    """检查Excel文件结构"""
    try:
        # 尝试读取Excel文件的前几行
        df = pd.read_excel(file_path, nrows=5)
        print(f"Excel文件列名: {list(df.columns)}")
        print(f"前5行数据:")
        print(df.head())
        return df.columns.tolist()
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def standardize_column_names(df: pd.DataFrame) -> pd.DataFrame:
    """标准化列名"""
    # 创建列名映射
    column_mapping = {}

    for col in df.columns:
        col_lower = str(col).lower()
        if 'item_id' in col_lower or (col_lower == 'id'):
            column_mapping[col] = '商品ID'
        elif '商品名' in col_lower and '商品名' not in column_mapping.values():
            column_mapping[col] = '商品名'
        elif ('类目' in col_lower or '分类' in col_lower) and '类目' not in column_mapping.values():
            # 优先选择物理类目
            if '物理' in col_lower:
                column_mapping[col] = '类目'
            elif '类目' not in column_mapping.values():
                column_mapping[col] = '类目'
        elif ('描述' in col_lower or '详情' in col_lower) and '商品描述' not in column_mapping.values():
            column_mapping[col] = '商品描述'
        elif ('季节' in col_lower and '标签' in col_lower) and '人工标签' not in column_mapping.values():
            column_mapping[col] = '人工标签'

    # 重命名列
    df_renamed = df.rename(columns=column_mapping)
    print(f"列名映射: {column_mapping}")

    return df_renamed

def process_excel_file(file_path: str, sample_size: int = None):
    """处理Excel文件"""
    print(f"开始处理文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    # 检查Excel结构
    columns = check_excel_structure(file_path)
    if columns is None:
        return None
    
    try:
        # 读取完整数据
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，共{len(df)}条记录")
        
        # 标准化列名
        df = standardize_column_names(df)
        
        # 如果指定了样本大小，则随机采样
        if sample_size and sample_size < len(df):
            df = df.sample(n=sample_size, random_state=42)
            print(f"随机采样{sample_size}条记录进行测试")
        
        # 显示处理后的数据结构
        print(f"\n处理后的数据结构:")
        print(f"列名: {list(df.columns)}")
        print(f"数据形状: {df.shape}")
        
        # 显示前几行数据
        print(f"\n前3行数据:")
        for col in df.columns:
            if col in ['商品ID', '商品名', '类目', '人工标签']:
                print(f"{col}: {df[col].head(3).values.tolist()}")
        
        return df
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

def run_classification(df: pd.DataFrame, use_ai: bool = True, batch_size: int = 10):
    """运行分类任务"""
    # API配置
    API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    
    # 创建分类器
    classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
    
    # 添加预测列
    df['关键词预测标签'] = ''
    df['AI预测标签'] = ''
    df['标签匹配'] = ''
    
    print(f"\n开始分类，共{len(df)}条记录...")
    
    # 分批处理
    for i in range(0, len(df), batch_size):
        batch_end = min(i + batch_size, len(df))
        print(f"处理第 {i+1}-{batch_end} 条记录...")
        
        for idx in range(i, batch_end):
            row = df.iloc[idx]
            product_info = row.to_dict()
            
            # 关键词分类
            keyword_label = classifier.classify_single_product(product_info)
            df.at[idx, '关键词预测标签'] = keyword_label
            
            # AI分类
            if use_ai:
                try:
                    ai_label = classifier.classify_with_ai(product_info)
                    df.at[idx, 'AI预测标签'] = ai_label
                except Exception as e:
                    print(f"AI分类失败 (第{idx+1}条): {e}")
                    df.at[idx, 'AI预测标签'] = keyword_label
            else:
                df.at[idx, 'AI预测标签'] = keyword_label
            
            # 检查匹配情况
            manual_label = row.get('人工标签', '')
            ai_predicted = df.at[idx, 'AI预测标签']
            
            if manual_label and ai_predicted:
                df.at[idx, '标签匹配'] = '匹配' if str(manual_label) == str(ai_predicted) else '不匹配'
    
    return df

def analyze_results(df: pd.DataFrame):
    """分析结果"""
    print("\n" + "="*60)
    print("分析结果")
    print("="*60)
    
    # 检查是否有人工标签
    manual_label_col = None
    for col in ['人工标签', '季节标签', '标签']:
        if col in df.columns and df[col].notna().sum() > 0:
            manual_label_col = col
            break
    
    if manual_label_col is None:
        print("未找到有效的人工标签，无法计算准确率")
        print("\n预测结果分布:")
        if 'AI预测标签' in df.columns:
            print(df['AI预测标签'].value_counts())
        return
    
    # 过滤有效数据
    valid_data = df[df[manual_label_col].notna() & df['AI预测标签'].notna()]
    
    if len(valid_data) == 0:
        print("没有有效的对比数据")
        return
    
    print(f"有效对比数据: {len(valid_data)} 条")
    
    # 计算总体准确率
    total_correct = (valid_data[manual_label_col].astype(str) == valid_data['AI预测标签'].astype(str)).sum()
    total_accuracy = total_correct / len(valid_data)
    
    print(f"\n总体准确率: {total_accuracy:.4f} ({total_correct}/{len(valid_data)})")
    
    # 计算各标签的准确率
    print(f"\n各标签准确率:")
    for label in ['全季', 'SS', 'AW']:
        label_data = valid_data[valid_data[manual_label_col].astype(str) == label]
        if len(label_data) > 0:
            label_correct = (label_data[manual_label_col].astype(str) == label_data['AI预测标签'].astype(str)).sum()
            accuracy = label_correct / len(label_data)
            target = 0.95 if label == '全季' else 0.90
            status = "✓ 达成" if accuracy >= target else "✗ 未达成"
            print(f"  {label}: {accuracy:.4f} ({label_correct}/{len(label_data)}) 目标:{target:.2f} {status}")
        else:
            print(f"  {label}: 无数据")
    
    # 混淆矩阵
    print(f"\n标签分布:")
    print("人工标签分布:")
    print(valid_data[manual_label_col].value_counts())
    print("\nAI预测分布:")
    print(valid_data['AI预测标签'].value_counts())
    
    # 不匹配的商品
    mismatched = valid_data[valid_data[manual_label_col].astype(str) != valid_data['AI预测标签'].astype(str)]
    if len(mismatched) > 0:
        print(f"\n不匹配商品: {len(mismatched)} 条")
        
        # 保存不匹配商品
        output_cols = []
        for col in ['商品ID', '商品名', '类目', '商品描述', manual_label_col, 'AI预测标签', '关键词预测标签']:
            if col in mismatched.columns:
                output_cols.append(col)
        
        if output_cols:
            mismatched_file = f"不匹配商品_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            mismatched[output_cols].to_csv(mismatched_file, index=False, encoding='utf-8-sig')
            print(f"不匹配商品已保存到: {mismatched_file}")
            
            # 显示前几个不匹配的商品
            print(f"\n前5个不匹配商品:")
            display_cols = ['商品名', manual_label_col, 'AI预测标签']
            display_cols = [col for col in display_cols if col in mismatched.columns]
            if display_cols:
                print(mismatched[display_cols].head().to_string(index=False))

def main():
    """主函数"""
    excel_file = "上架商品季节标签.xlsx"
    
    # 处理Excel文件
    df = process_excel_file(excel_file, sample_size=50)  # 先用50条数据测试
    
    if df is not None:
        # 运行分类
        df_result = run_classification(df, use_ai=True, batch_size=5)
        
        # 分析结果
        analyze_results(df_result)
        
        # 保存结果
        result_file = f"季节标签分类结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df_result.to_excel(result_file, index=False)
        print(f"\n完整结果已保存到: {result_file}")
        
        # 生成CSV格式的结果表格
        if '商品ID' in df_result.columns:
            csv_result = df_result[['商品ID', 'AI预测标签']].copy()
        else:
            csv_result = df_result[['AI预测标签']].copy()
            csv_result.index.name = '序号'
        
        csv_file = f"商品季节标签_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        csv_result.to_csv(csv_file, index=True, encoding='utf-8-sig')
        print(f"CSV格式结果已保存到: {csv_file}")

if __name__ == "__main__":
    main()
