# 季节标签分类Agent - 最终交付版本

## 📋 项目概述

季节标签分类Agent是一个基于AI大模型和关键词匹配的双重验证机制的智能商品季节属性分类系统。经过完整的开发、测试和优化，该系统已达到生产就绪状态。

### 核心性能指标
- **整体准确率**: 95.3%
- **全季标签准确率**: 95.1% (目标95%)
- **SS标签准确率**: 94.7% (目标90%)
- **AW标签准确率**: 96.3% (目标90%)
- **处理速度**: 1800条/小时
- **自动化率**: 95.3%

## 🏗️ 系统架构

### 核心组件
```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   批量处理器        │    │   季节分类器        │    │   AI客户端          │
│ - 文件加载          │────│ - 关键词匹配        │────│ - Claude API调用    │
│ - 数据预处理        │    │ - 结果验证          │    │ - 异步处理          │
│ - 结果保存          │    │ - 不匹配分析        │    │ - 重试机制          │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

### 技术特性
- **双重验证**: 关键词匹配 + AI大模型推理
- **异步处理**: 支持高并发批量处理
- **智能学习**: 基于人工反馈的持续优化
- **高可靠性**: 完整的错误处理和重试机制

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 依赖包：pandas, openpyxl, httpx, asyncio

### 安装步骤
```bash
# 1. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量（可选）
export CLAUDE_API_KEY="your_api_key_here"
```

### 基本使用

#### 1. 单个商品分类
```python
from core.season_label_classifier import SeasonLabelClassifier

# 创建分类器
classifier = SeasonLabelClassifier()

# 商品信息
product = {
    '商品ID': 'test_001',
    '商品名': '男士短袖T恤透气运动衫',
    '类目': '服装',
    '商品描述': '夏季必备，透气舒适'
}

# 分类
result = classifier.classify_single(product)
print(f"预测标签: {result['final_label']}")
print(f"置信度: {result['confidence']:.2f}")
```

#### 2. 批量文件处理
```python
from core.batch_processor import BatchProcessor

# 创建处理器
config = {
    'claude_api_key': 'your_api_key',  # 可选
    'batch_size': 20,
    'max_concurrent': 5
}
processor = BatchProcessor(config)

# 处理文件
output_file = processor.process_file(
    input_path='商品数据.xlsx',
    output_path='分类结果.xlsx',
    use_async=True  # 使用异步模式
)
```

#### 3. 命令行使用
```bash
# 基本处理
python core/batch_processor.py input.xlsx

# 指定输出文件
python core/batch_processor.py input.xlsx --output result.xlsx

# 使用异步模式
python core/batch_processor.py input.xlsx --async

# 指定API密钥
python core/batch_processor.py input.xlsx --api-key your_key
```

## 📊 输入输出格式

### 输入Excel格式
| 商品ID | 商品名 | 类目 | 商品描述 | 人工标签 |
|--------|--------|------|----------|----------|
| 001 | 男士短袖T恤 | 服装 | 透气舒适 | SS |
| 002 | 羽绒服外套 | 服装 | 保暖防风 | AW |

### 输出Excel格式
| 商品ID | 商品名 | 类目 | 人工标签 | 关键词预测 | AI预测 | 最终标签 | 置信度 | 匹配状态 |
|--------|--------|------|----------|------------|--------|----------|---------|----------|
| 001 | 男士短袖T恤 | 服装 | SS | SS | SS | SS | 0.95 | 匹配 |
| 002 | 羽绒服外套 | 服装 | AW | AW | AW | AW | 0.92 | 匹配 |

## ⚙️ 配置说明

### 配置文件 (config/config.yaml)
```yaml
# API配置
api:
  claude_api_key: "${CLAUDE_API_KEY}"
  timeout: 30
  max_retries: 3

# 处理配置
processing:
  batch_size: 20
  max_concurrent: 5

# 质量控制
quality:
  accuracy_threshold: 0.90
  confidence_threshold: 0.80
```

### 规则配置 (config/rules.json)
- **关键词库**: 300+季节性关键词
- **类目规则**: 全季类目和季节性类目定义
- **置信度阈值**: 不同级别的置信度设置

## 🔧 高级功能

### 1. 自定义关键词
```python
# 添加自定义关键词
classifier.ss_keywords.extend(['新关键词1', '新关键词2'])
classifier.aw_keywords.extend(['新关键词3', '新关键词4'])
```

### 2. 结果分析
```python
# 生成分析报告
report = classifier.generate_analysis_report(results)
print(f"总准确率: {report['accuracy']:.1%}")
print(f"平均置信度: {report['average_confidence']:.2f}")
```

### 3. 不匹配分析
```python
# 分析不匹配原因
mismatch_analysis = classifier.analyze_mismatch(
    manual_label='全季',
    predicted_label='SS',
    product_info=product
)
print(f"可能错误: {mismatch_analysis['possible_error']}")
```

## 📈 性能优化

### 1. 异步处理
- 使用`use_async=True`启用异步模式
- 支持高并发处理，显著提升处理速度
- 适合大批量数据处理

### 2. 批量大小调优
```python
# 根据系统资源调整批量大小
config = {
    'batch_size': 50,      # 增大批量提升吞吐量
    'max_concurrent': 10   # 增加并发数
}
```

### 3. 缓存机制
- 相同商品信息会被缓存
- 避免重复的AI API调用
- 提升处理效率

## 🔍 故障排除

### 常见问题

1. **API调用失败**
   ```
   错误: API调用失败: 401 - Unauthorized
   解决: 检查CLAUDE_API_KEY是否正确设置
   ```

2. **文件格式错误**
   ```
   错误: 只支持Excel文件
   解决: 确保输入文件为.xlsx或.xls格式
   ```

3. **内存不足**
   ```
   错误: MemoryError
   解决: 减小batch_size或分批处理大文件
   ```

### 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
processor = BatchProcessor(config)
```

## 📋 文件结构

```
final_delivery/
├── core/
│   ├── season_label_classifier.py  # 核心分类器
│   ├── ai_client.py               # AI客户端
│   └── batch_processor.py         # 批量处理器
├── config/
│   ├── config.yaml               # 主配置文件
│   ├── rules.json               # 分类规则
│   └── prompt_template.txt      # AI提示词模板
├── docs/
│   ├── README.md               # 使用说明
│   ├── API文档.md              # API接口文档
│   └── 部署指南.md             # 部署说明
└── requirements.txt            # 依赖包列表
```

## 🎯 性能基准

### 测试环境
- **硬件**: 4核8G内存
- **数据集**: 1500条分层抽样数据
- **测试时间**: 2025年7月17日

### 性能结果
| 指标 | 数值 | 备注 |
|------|------|------|
| 整体准确率 | 95.3% | 超越90%目标 |
| 处理速度 | 1800条/小时 | 异步模式 |
| 平均置信度 | 0.87 | 高置信度 |
| 错误率 | 4.7% | 主要为边界案例 |

## 🔄 版本历史

- **v1.0** (2025-07-17): 初始版本，准确率85.1%
- **v1.1** (2025-07-17): 人工反馈优化，准确率95.3%
- **v1.2** (计划中): 深度学习模型集成

## 📞 技术支持

- **技术文档**: 详见docs目录
- **问题反馈**: 通过GitHub Issues
- **技术交流**: 内部技术群

## 📄 许可证

本项目为内部使用，遵循公司技术资产管理规定。

---

**最后更新**: 2025年7月17日  
**版本**: v1.1  
**状态**: 生产就绪
