#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单分析验证结果
"""

import pandas as pd
import os
from datetime import datetime

def main():
    """主函数"""
    print("🔍 分析验证结果...")
    
    try:
        # 直接读取最新的验证结果文件
        df = pd.read_excel("改进效果验证_20250717_143356.xlsx")
        print(f"📊 读取验证结果，共 {len(df)} 条记录")
        
        # 基本统计
        total_count = len(df)
        match_count = (df['匹配情况'] == '匹配').sum()
        accuracy = match_count / total_count
        
        print(f"\n📊 基本统计:")
        print(f"总样本数: {total_count}")
        print(f"匹配数: {match_count}")
        print(f"准确率: {accuracy:.1%}")
        
        # 各标签统计
        print(f"\n📊 各标签表现:")
        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) > 0:
                label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
                label_accuracy = label_correct / len(label_data)
                print(f"{label}: {label_accuracy:.1%} ({label_correct}/{len(label_data)})")
        
        # 分析不匹配情况
        mismatched = df[df['匹配情况'] == '不匹配']
        print(f"\n📊 不匹配分析 ({len(mismatched)} 条):")
        
        # SS标签的不匹配情况
        ss_mismatched = mismatched[mismatched['人工标签'] == 'SS']
        if len(ss_mismatched) > 0:
            print(f"\nSS标签不匹配 ({len(ss_mismatched)} 条):")
            ss_predictions = ss_mismatched['AI预测'].value_counts()
            for pred, count in ss_predictions.items():
                print(f"  SS → {pred}: {count} 条")
            
            print("  典型案例:")
            for idx, row in ss_mismatched.head(3).iterrows():
                print(f"    {row['商品名'][:40]}... → 人工:SS, AI:{row['AI预测']}")
        
        # AW标签的不匹配情况
        aw_mismatched = mismatched[mismatched['人工标签'] == 'AW']
        if len(aw_mismatched) > 0:
            print(f"\nAW标签不匹配 ({len(aw_mismatched)} 条):")
            aw_predictions = aw_mismatched['AI预测'].value_counts()
            for pred, count in aw_predictions.items():
                print(f"  AW → {pred}: {count} 条")
            
            print("  典型案例:")
            for idx, row in aw_mismatched.head(3).iterrows():
                print(f"    {row['商品名'][:40]}... → 人工:AW, AI:{row['AI预测']}")
        
        # 关键词 vs AI对比
        keyword_correct = (df['关键词预测'] == df['人工标签']).sum()
        ai_correct = (df['AI预测'] == df['人工标签']).sum()
        keyword_accuracy = keyword_correct / total_count
        ai_accuracy = ai_correct / total_count
        
        print(f"\n📊 方法对比:")
        print(f"关键词预测准确率: {keyword_accuracy:.1%} ({keyword_correct}/{total_count})")
        print(f"AI预测准确率: {ai_accuracy:.1%} ({ai_correct}/{total_count})")
        
        # 分析原因
        print(f"\n🔍 可能的原因分析:")
        print("1. 测试样本不同：这次是150条随机样本，之前是1500条分层样本")
        print("2. 样本难度：随机样本可能包含更多边界模糊的商品")
        print("3. API变化：AI模型响应可能存在时间差异")
        print("4. 数据修正影响：修正了17个商品后，整体数据分布发生变化")
        
        print(f"\n💡 建议:")
        print("1. 使用相同的1500条测试集重新验证")
        print("2. 重点分析修正的17个商品是否预测正确")
        print("3. 检查关键词匹配是否按预期工作")
        
        # 检查修正的商品是否在测试集中
        corrected_ids = [4037797, 4075229, 4068274, 4066219, 4068258, 4073369, 
                        4079314, 4049165, 4039846, 4082014, 4079365, 4025111, 
                        3992516, 3993285, 3990540, 4089779, 3879017]
        
        corrected_in_test = df[df['商品ID'].isin(corrected_ids)]
        if len(corrected_in_test) > 0:
            print(f"\n📊 修正商品在测试集中的表现 ({len(corrected_in_test)} 条):")
            corrected_correct = (corrected_in_test['人工标签'] == corrected_in_test['AI预测']).sum()
            corrected_accuracy = corrected_correct / len(corrected_in_test)
            print(f"修正商品准确率: {corrected_accuracy:.1%} ({corrected_correct}/{len(corrected_in_test)})")
            
            for idx, row in corrected_in_test.iterrows():
                status = "✅" if row['人工标签'] == row['AI预测'] else "❌"
                print(f"  {row['商品ID']}: {row['商品名'][:30]}... → {row['人工标签']} vs {row['AI预测']} {status}")
        else:
            print(f"\n📊 修正的17个商品都不在这次的测试集中")
            print("这解释了为什么看不到明显的改进效果")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
