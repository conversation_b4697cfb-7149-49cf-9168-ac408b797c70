#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
季节标签分类器
基于商品信息判断季节标签：全季、SS（春夏）、AW（秋冬）
"""

import pandas as pd
import numpy as np
import re
from openai import OpenAI
from typing import Dict, List, Tuple
import json
import time
from datetime import datetime

class SeasonLabelClassifier:
    def __init__(self, api_key: str, base_url: str):
        """初始化分类器"""
        self.client = OpenAI(api_key=api_key, base_url=base_url)
        self.load_prompt()
        
        # 定义关键词
        self.aw_keywords = [
            '羽绒', '冬', '保暖', '加厚', '防风', '毛呢', '皮草', '羊毛', 
            '冲锋衣', '雪地靴', '秋', '厚', '抗寒', '棉拖', '靴子',
            '厚款', '绒面', '御寒', '秋冬', '加绒', '毛绒', '棉服', '厚实', '保温'
        ]
        
        self.ss_keywords = [
            '夏', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气',
            '凉爽', '薄款', '速干', '冰感', '遮阳', '轻薄', '网眼', '排汗', '凉感', '夏装'
        ]
        
        self.all_season_categories = [
            '食品饮料', '母婴', '家具家装', '电器', '抽纸面巾', '休闲零食', 
            '滋补保健', '餐厨', '家居用品', '美妆个护', '床垫', '宠物', 
            '箱包', '食品酒水', '居家日用', '美食酒水', '汽车用品', 
            '特殊类目', '箱包配饰', '医疗保健', '运动户外', '衬衫', 
            '连衣裙/半身裙', '内衣内裤', '运动鞋', '裤子', '风衣', 
            '袜子', '徒步运动衣裤', '卫衣', '商务衣裤'
        ]

    def load_prompt(self):
        """加载提示词"""
        try:
            with open('prompts/prompt', 'r', encoding='utf-8') as f:
                self.prompt_template = f.read()
        except FileNotFoundError:
            print("警告：未找到prompt文件，使用默认提示词")
            self.prompt_template = """
            基于以下逻辑对商品的季节标签进行判断，产出季节标签结果：
            步骤1: 检查类目信息（物理类目优先）
            步骤2: 检查商品名和商品可能使用的季节
            步骤3: 检查商品描述（补充作用）
            
            季节标签只能是：全季、SS、AW
            
            请根据商品信息判断季节标签，只返回标签名称。
            """

    def classify_by_keywords(self, text: str) -> str:
        """基于关键词进行初步分类"""
        if not text:
            return "全季"
            
        text = str(text).lower()
        
        # 检查AW关键词
        for keyword in self.aw_keywords:
            if keyword in text:
                return "AW"
        
        # 检查SS关键词
        for keyword in self.ss_keywords:
            if keyword in text:
                return "SS"
        
        return "全季"

    def classify_by_category(self, category: str) -> str:
        """基于类目进行分类"""
        if not category:
            return "全季"
            
        category = str(category)
        
        # 检查是否为全季类目
        for all_season_cat in self.all_season_categories:
            if all_season_cat in category:
                return "全季"
        
        # 检查AW类目关键词
        aw_cat_keywords = ['羽绒服', '毛呢', '冲锋衣', '保暖', '户外外套', '靴子', '棉拖']
        for keyword in aw_cat_keywords:
            if keyword in category:
                return "AW"
        
        # 检查SS类目关键词
        ss_cat_keywords = ['短袖', '凉鞋', '泳装', '防晒']
        for keyword in ss_cat_keywords:
            if keyword in category:
                return "SS"
        
        return "全季"

    def classify_single_product(self, product_info: Dict) -> str:
        """对单个商品进行季节标签分类"""
        # 步骤1：检查类目
        category = product_info.get('类目', '') or product_info.get('物理类目', '')
        category_result = self.classify_by_category(category)
        if category_result != "全季":
            return category_result
        
        # 步骤2：检查商品名
        product_name = product_info.get('商品名', '') or product_info.get('商品名称', '')
        name_result = self.classify_by_keywords(product_name)
        if name_result != "全季":
            return name_result
        
        # 步骤3：检查商品描述
        description = product_info.get('商品描述', '') or product_info.get('描述', '')
        desc_result = self.classify_by_keywords(description)
        
        return desc_result

    def classify_with_ai(self, product_info: Dict) -> str:
        """使用AI模型进行分类"""
        try:
            # 构建商品信息文本
            info_text = f"""
            商品ID: {product_info.get('商品ID', 'N/A')}
            商品名称: {product_info.get('商品名', '') or product_info.get('商品名称', '')}
            类目: {product_info.get('类目', '') or product_info.get('物理类目', '')}
            商品描述: {product_info.get('商品描述', '') or product_info.get('描述', '')}
            """
            
            system_prompt = """你是一个专业的电商商品季节性分类专家。请根据商品信息判断季节标签。
            季节标签只能是以下三个之一：全季、SS、AW
            
            判断逻辑：
            1. 全季：适用于一年四季的商品，如食品、电器、日用品等
            2. SS（春夏）：主要在春夏季节使用的商品，如短袖、凉鞋、防晒用品等
            3. AW（秋冬）：主要在秋冬季节使用的商品，如羽绒服、保暖用品、靴子等
            
            请只返回标签名称，不要其他解释。"""
            
            user_prompt = f"{self.prompt_template}\n\n商品信息：\n{info_text}"
            
            response = self.client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=10
            )
            
            result = response.choices[0].message.content.strip()
            
            # 标准化输出
            if "SS" in result or "春夏" in result:
                return "SS"
            elif "AW" in result or "秋冬" in result:
                return "AW"
            else:
                return "全季"
                
        except Exception as e:
            print(f"AI分类失败: {e}")
            # 回退到关键词分类
            return self.classify_single_product(product_info)

    def process_excel_file(self, file_path: str, use_ai: bool = True) -> pd.DataFrame:
        """处理Excel文件中的商品数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            print(f"成功读取Excel文件，共{len(df)}条记录")
            
            # 添加AI预测列
            df['AI预测标签'] = ''
            df['关键词预测标签'] = ''
            df['标签匹配'] = ''
            
            # 处理每个商品
            for idx, row in df.iterrows():
                product_info = row.to_dict()
                
                # 关键词分类
                keyword_label = self.classify_single_product(product_info)
                df.at[idx, '关键词预测标签'] = keyword_label
                
                # AI分类
                if use_ai:
                    ai_label = self.classify_with_ai(product_info)
                    df.at[idx, 'AI预测标签'] = ai_label
                else:
                    df.at[idx, 'AI预测标签'] = keyword_label
                
                # 检查是否与人工标签匹配
                manual_label = row.get('人工标签', '') or row.get('季节标签', '')
                ai_predicted = df.at[idx, 'AI预测标签']
                
                if manual_label and ai_predicted:
                    df.at[idx, '标签匹配'] = '匹配' if manual_label == ai_predicted else '不匹配'
                
                # 显示进度
                if (idx + 1) % 10 == 0:
                    print(f"已处理 {idx + 1}/{len(df)} 条记录")
                
                # 避免API调用过快
                if use_ai:
                    time.sleep(0.1)
            
            return df
            
        except Exception as e:
            print(f"处理Excel文件时出错: {e}")
            return None

    def calculate_accuracy(self, df: pd.DataFrame) -> Dict:
        """计算准确率"""
        if df is None or df.empty:
            return {}
        
        # 获取有人工标签的数据
        manual_label_col = None
        for col in ['人工标签', '季节标签', '标签']:
            if col in df.columns:
                manual_label_col = col
                break
        
        if manual_label_col is None:
            print("未找到人工标签列")
            return {}
        
        # 过滤有效数据
        valid_data = df[df[manual_label_col].notna() & df['AI预测标签'].notna()]
        
        if len(valid_data) == 0:
            print("没有有效的对比数据")
            return {}
        
        # 计算总体准确率
        total_correct = (valid_data[manual_label_col] == valid_data['AI预测标签']).sum()
        total_accuracy = total_correct / len(valid_data)
        
        # 计算各标签的准确率
        accuracy_by_label = {}
        for label in ['全季', 'SS', 'AW']:
            label_data = valid_data[valid_data[manual_label_col] == label]
            if len(label_data) > 0:
                label_correct = (label_data[manual_label_col] == label_data['AI预测标签']).sum()
                accuracy_by_label[label] = label_correct / len(label_data)
            else:
                accuracy_by_label[label] = 0.0
        
        return {
            '总体准确率': total_accuracy,
            '全季准确率': accuracy_by_label['全季'],
            'SS准确率': accuracy_by_label['SS'],
            'AW准确率': accuracy_by_label['AW'],
            '总样本数': len(valid_data),
            '各标签样本数': {
                '全季': len(valid_data[valid_data[manual_label_col] == '全季']),
                'SS': len(valid_data[valid_data[manual_label_col] == 'SS']),
                'AW': len(valid_data[valid_data[manual_label_col] == 'AW'])
            }
        }

    def find_mismatched_items(self, df: pd.DataFrame) -> pd.DataFrame:
        """找出不匹配的商品"""
        if df is None or df.empty:
            return pd.DataFrame()
        
        # 找出标签不匹配的商品
        mismatched = df[df['标签匹配'] == '不匹配'].copy()
        
        if len(mismatched) == 0:
            print("所有商品标签都匹配！")
            return pd.DataFrame()
        
        # 选择关键列用于分析
        key_columns = []
        for col in ['商品ID', '商品名', '商品名称', '类目', '物理类目', '商品描述', '描述']:
            if col in mismatched.columns:
                key_columns.append(col)
        
        key_columns.extend(['人工标签', 'AI预测标签', '关键词预测标签'])
        
        # 过滤存在的列
        available_columns = [col for col in key_columns if col in mismatched.columns]
        
        return mismatched[available_columns]


def main():
    """主函数"""
    # API配置
    API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    
    # 创建分类器
    classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
    
    # 处理Excel文件
    excel_file = "上架商品季节标签.xlsx"
    print(f"开始处理文件: {excel_file}")
    
    # 处理数据
    df_result = classifier.process_excel_file(excel_file, use_ai=True)
    
    if df_result is not None:
        # 计算准确率
        accuracy_stats = classifier.calculate_accuracy(df_result)
        
        print("\n=== 准确率统计 ===")
        for key, value in accuracy_stats.items():
            if isinstance(value, dict):
                print(f"{key}:")
                for k, v in value.items():
                    print(f"  {k}: {v}")
            else:
                print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")
        
        # 检查是否达到目标准确率
        target_accuracy = {'全季': 0.95, 'SS': 0.90, 'AW': 0.90}
        print("\n=== 目标达成情况 ===")
        for label, target in target_accuracy.items():
            actual = accuracy_stats.get(f'{label}准确率', 0)
            status = "✓ 达成" if actual >= target else "✗ 未达成"
            print(f"{label}: {actual:.4f} (目标: {target:.4f}) {status}")
        
        # 找出不匹配的商品
        mismatched_items = classifier.find_mismatched_items(df_result)
        
        if not mismatched_items.empty:
            print(f"\n=== 发现 {len(mismatched_items)} 个不匹配商品 ===")
            
            # 保存不匹配商品到CSV
            output_file = f"不匹配商品分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            mismatched_items.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"不匹配商品已保存到: {output_file}")
            
            # 显示前几个不匹配的商品
            print("\n前5个不匹配商品:")
            print(mismatched_items.head().to_string(index=False))
        
        # 保存完整结果
        result_file = f"季节标签分类结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df_result.to_excel(result_file, index=False)
        print(f"\n完整结果已保存到: {result_file}")
        
        # 生成CSV格式的结果表格（只包含商品ID和标签）
        csv_result = df_result[['商品ID', 'AI预测标签']].copy() if '商品ID' in df_result.columns else df_result[['AI预测标签']].copy()
        csv_file = f"商品季节标签_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        csv_result.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"CSV格式结果已保存到: {csv_file}")


if __name__ == "__main__":
    main()
