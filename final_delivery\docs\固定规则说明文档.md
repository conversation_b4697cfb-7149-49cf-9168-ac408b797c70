# 季节标签分类固定规则说明文档

## 📋 文档概述

**文档版本**: v1.1  
**制定时间**: 2025年7月17日  
**适用范围**: 季节标签分类Agent系统  
**更新频率**: 季度更新或根据业务需求调整  

---

## 🎯 分类标准定义

### 标签定义
- **全季**: 适用于一年四季，无明显季节性限制的商品
- **SS（Spring/Summer）**: 主要在春夏季节（3-8月）使用的商品
- **AW（Autumn/Winter）**: 主要在秋冬季节（9-2月）使用的商品

### 判断原则
1. **主要使用场景**: 以商品的主要使用季节为准
2. **材质特性**: 考虑商品的保暖性、透气性等物理特性
3. **功能导向**: 以商品的核心功能和使用目的为判断依据
4. **保守原则**: 边界模糊时倾向于分类为全季

---

## 🔍 关键词识别规则

### SS（春夏）关键词体系

#### 一级关键词（强确定性 - 权重1.0）
**服装类**:
- `短袖` - 明显的夏季服装特征
- `短裤` - 夏季下装典型特征
- `短裙` - 女性夏季服装
- `吊带` - 夏季无袖服装
- `泳装` - 专用夏季服装
- `夏装` - 直接季节标识

**功能类**:
- `防晒` - 夏季专用功能
- `遮阳` - 夏季防护功能
- `夏季` - 直接季节标识

**鞋类**:
- `凉鞋` - 典型夏季鞋类

#### 二级关键词（高确定性 - 权重0.8-0.9）
**材质特性**:
- `透气` - 夏季舒适性要求
- `清凉` - 夏季体感需求
- `凉爽` - 温度调节功能
- `冰感` - 降温材质特性
- `凉感` - 体感降温

**设计特征**:
- `薄款` - 轻薄设计
- `轻薄` - 重量和厚度特征
- `网眼` - 透气设计
- `背心` - 无袖设计

#### 三级关键词（中等确定性 - 权重0.6-0.7）
**功能特性**:
- `速干` - 快干功能
- `排汗` - 汗液管理
- `网面` - 透气材质

**用品类**:
- `凉席` - 夏季床上用品
- `风扇` - 降温电器

### AW（秋冬）关键词体系

#### 一级关键词（强确定性 - 权重1.0）
**保暖特征**:
- `羽绒` - 顶级保暖材质
- `保暖` - 直接功能描述
- `冬季` - 直接季节标识
- `秋冬` - 季节组合标识

**鞋类**:
- `雪地靴` - 专用冬季鞋类

#### 二级关键词（高确定性 - 权重0.8-0.9）
**材质特征**:
- `加厚` - 厚度增强
- `毛呢` - 保暖面料
- `厚款` - 厚重设计
- `加绒` - 绒毛保暖
- `棉服` - 棉质保暖服装

**功能特征**:
- `御寒` - 抗寒功能
- `抗寒` - 低温防护

#### 三级关键词（中等确定性 - 权重0.6-0.7）
**材质类**:
- `毛绒` - 绒毛材质
- `厚实` - 厚重质感
- `绒面` - 绒毛表面
- `羊毛` - 天然保暖纤维

**功能类**:
- `保温` - 温度保持
- `防风` - 风寒防护

**用品类**:
- `暖宝宝` - 取暖用品
- `电热毯` - 保暖电器
- `取暖器` - 加热设备

### 全季关键词
- `四季通用` - 明确全季标识
- `全年适用` - 时间范围标识
- `不分季节` - 无季节限制
- `经典款` - 经典设计（弱特征）
- `基础款` - 基础款式（弱特征）

---

## 📂 类目分类规则

### 全季类目（默认全季判断）

#### 食品饮料类
- **范围**: 所有食品、饮料、酒水、零食、保健品
- **原因**: 食品消费不受季节限制
- **例外**: 无

#### 母婴用品类
- **范围**: 奶粉、尿布、婴儿用品、孕妇用品、儿童玩具
- **原因**: 婴幼儿需求不分季节
- **例外**: 婴儿季节性服装需进一步判断

#### 家具家装类
- **范围**: 家具、装修材料、五金工具、灯具、装饰品
- **原因**: 家居用品使用周期长，不受季节影响
- **例外**: 季节性装饰品（如圣诞装饰）

#### 电器数码类
- **范围**: 手机、电脑、家电、数码配件
- **原因**: 电子产品功能性为主，季节性较弱
- **例外**: 
  - 空调、风扇等温控设备需特殊判断
  - 取暖器等冬季专用电器

#### 美妆个护类
- **范围**: 化妆品、护肤品、洗护用品
- **原因**: 个人护理需求持续存在
- **例外**: 防晒产品归SS类

#### 其他全季类目
- 居家日用、宠物用品、箱包配饰、医疗保健、汽车用品

### 季节性类目（需进一步判断）

#### 服装类
**判断标准**:
1. **袖长**: 短袖→SS，长袖→需看厚度
2. **厚度**: 薄款→SS，厚款→AW
3. **材质**: 透气→SS，保暖→AW
4. **功能**: 防晒→SS，御寒→AW

**特殊规则**:
- 内衣类默认全季（除非有明显季节特征）
- 运动服装看材质和功能
- 工作服装看使用环境

#### 鞋靴类
**判断标准**:
1. **款式**: 凉鞋→SS，靴子→AW
2. **材质**: 透气网面→SS，毛绒厚底→AW
3. **功能**: 防滑防水→AW，透气排汗→SS

**特殊规则**:
- 运动鞋默认全季（除非有明显季节特征）
- 商务鞋默认全季
- 拖鞋需看材质（棉拖→AW，凉拖→SS）

#### 户外用品类
**判断标准**:
1. **使用环境**: 高温环境→SS，低温环境→AW
2. **功能特性**: 防晒遮阳→SS，保暖防寒→AW
3. **运动类型**: 水上运动→SS，雪上运动→AW

#### 家纺用品类
**判断标准**:
1. **厚度**: 薄被→SS，厚被→AW
2. **材质**: 凉席→SS，毛毯→AW
3. **功能**: 降温→SS，保暖→AW

---

## 🔧 具体判断规则

### 服装类商品细则

#### SS判断标准
1. **袖长特征**:
   - 短袖、无袖、吊带 → 直接判断SS
   - 七分袖 → 结合其他特征判断

2. **厚度特征**:
   - 薄款、轻薄、单层 → SS倾向
   - 透气、网眼材质 → SS倾向

3. **功能特征**:
   - 防晒、排汗、速干 → SS倾向
   - 凉爽、冰感 → SS倾向

#### AW判断标准
1. **保暖特征**:
   - 加厚、保暖、御寒 → 直接判断AW
   - 羽绒、棉服 → 直接判断AW

2. **材质特征**:
   - 羊毛、绒面、毛呢 → AW倾向
   - 加绒、毛绒 → AW倾向

3. **功能特征**:
   - 防风、抗寒、保温 → AW倾向

#### 全季判断标准
1. **基础款式**:
   - 普通T恤、衬衫、牛仔裤 → 全季
   - 无明显季节特征 → 全季

2. **多季适用**:
   - 明确标注"四季可穿" → 全季
   - 经典款、基础款 → 全季倾向

### 鞋靴类商品细则

#### SS判断标准
1. **款式特征**:
   - 凉鞋、洞洞鞋、拖鞋 → 直接判断SS
   - 露脚趾设计 → SS倾向

2. **材质特征**:
   - 透气网面、网眼布料 → SS倾向
   - 轻薄材质 → SS倾向

#### AW判断标准
1. **款式特征**:
   - 靴子、雪地靴、棉鞋 → 直接判断AW
   - 高帮、包裹性强 → AW倾向

2. **材质特征**:
   - 毛绒、厚底、保暖材质 → AW倾向
   - 防水、防滑 → AW倾向

#### 全季判断标准
1. **运动鞋类**:
   - 普通运动鞋、跑步鞋 → 全季
   - 无明显季节特征 → 全季

2. **商务鞋类**:
   - 皮鞋、正装鞋 → 全季
   - 休闲鞋、帆布鞋 → 全季

---

## ⚖️ 边界情况处理规则

### 多功能商品
1. **以主要功能为准**: 如保温杯主要功能是保温→AW
2. **考虑使用频率**: 哪个季节使用更频繁
3. **参考营销定位**: 商家如何定位该商品

**示例**:
- 保温杯 → 虽然全年可用，但主要功能是保温 → AW
- 防晒衣 → 虽然是衣服，但主要功能是防晒 → SS

### 地域差异商品
1. **以大部分地区为准**: 不考虑极端气候地区
2. **以主流市场为准**: 以主要销售地区的季节特性为准
3. **避免过度细分**: 不因地域差异过度细分

**示例**:
- 羽绒服 → 在南方冬季也有需求 → AW
- 短袖 → 在北方夏季也是主流 → SS

### 材质复合商品
1. **以主要材质为准**: 如主要是棉质但有少量绒→全季
2. **以功能导向为准**: 主要功能决定分类
3. **考虑整体设计**: 整体设计意图和使用场景

**示例**:
- 棉质加绒内衣 → 主要功能是保暖 → AW
- 透气运动鞋（少量保暖设计）→ 主要功能是透气 → SS

### 价格/品质标记商品
1. **忽略价格标记**: "特价"、"促销"不影响季节性判断
2. **忽略品质标记**: "微瑕品"、"尾货"不影响分类
3. **关注商品本质**: 以商品本身特性为准

**示例**:
- 【微瑕品】羽绒服 → 忽略微瑕品标记 → AW
- 【特价】短袖T恤 → 忽略特价标记 → SS

---

## 📊 置信度评估规则

### 置信度等级定义
- **高置信度 (0.8-1.0)**: 有明确季节性特征，判断确定性强
- **中置信度 (0.6-0.8)**: 有一定季节性倾向，但不够明确
- **低置信度 (0.4-0.6)**: 季节性特征不明显，判断不确定

### 置信度计算方法
1. **关键词匹配分数**: 匹配关键词数量和权重
   ```
   基础分数 = 匹配关键词数 / 总关键词数
   权重调整 = Σ(匹配关键词权重) / 匹配关键词数
   最终分数 = min(基础分数 × 权重调整 × 2.5, 1.0)
   ```

2. **类目基础分数**: 不同类目的基础置信度
   - 全季类目: 0.9
   - 季节性类目: 0.7
   - 未知类目: 0.5

3. **特征明显程度**: 季节性特征的明显程度
   - 强特征关键词: +0.2
   - 中等特征关键词: +0.1
   - 弱特征关键词: +0.05

4. **一致性验证**: 多个判断依据的一致性
   - 关键词与AI一致: +0.1
   - 关键词与AI不一致: -0.1

### 置信度应用规则
- **高置信度**: 可直接采用分类结果
- **中置信度**: 建议人工复核
- **低置信度**: 必须人工审核

### 实际应用示例
```python
# 示例1: 明显SS商品
商品名: "男士短袖T恤透气运动衫"
关键词匹配: ["短袖", "透气"] → 基础分数0.8
权重调整: (1.0 + 0.9) / 2 = 0.95
最终置信度: min(0.8 × 0.95 × 2.5, 1.0) = 1.0

# 示例2: 边界商品
商品名: "男士休闲衬衫"
关键词匹配: [] → 基础分数0.0
类目基础: 0.7 (服装类)
最终置信度: 0.7
```

---

## 🔄 规则更新机制

### 更新触发条件
1. **准确率下降**: 某类商品准确率低于目标值
2. **新商品类型**: 出现新的商品类型或特征
3. **业务需求变化**: 业务对分类标准的调整需求
4. **季节性变化**: 季节性商品特征的变化

### 更新流程
1. **数据分析**: 分析错误案例和新商品特征
2. **规则制定**: 制定新的分类规则或调整现有规则
3. **测试验证**: 在测试集上验证新规则的效果
4. **文档更新**: 更新规则文档和系统配置
5. **培训推广**: 对相关人员进行培训

### 版本管理
- **主版本**: 重大规则调整时更新（如v1.0→v2.0）
- **次版本**: 规则细化或新增时更新（如v1.0→v1.1）
- **修订版**: 错误修正或微调时更新（如v1.1→v1.1.1）

---

## 📋 质量控制标准

### 准确率目标
- **全季商品**: ≥95%准确率
- **SS商品**: ≥90%准确率
- **AW商品**: ≥90%准确率
- **整体准确率**: ≥90%

### 一致性要求
1. **同类商品一致**: 相同特征的商品应有相同分类
2. **关键词一致**: 包含相同关键词的商品分类应一致
3. **逻辑一致**: 判断逻辑应前后一致

### 可解释性要求
1. **判断依据明确**: 每个分类都应有明确的判断依据
2. **规则可追溯**: 分类结果应能追溯到具体规则
3. **异常可解释**: 特殊情况应有合理解释

---

## 📞 联系方式

**规则制定**: 产品团队 + 业务团队  
**技术实现**: 技术开发团队  
**质量监控**: 数据质量团队  

**问题反馈**: [反馈邮箱]  
**规则咨询**: [咨询联系方式]  
**文档维护**: [文档负责人]  

---

**文档制定时间**: 2025年7月17日  
**下次审核时间**: 2025年10月17日  
**文档状态**: 正式版本  
**适用系统**: 季节标签分类Agent v1.1
