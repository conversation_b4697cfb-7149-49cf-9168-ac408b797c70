# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
aiofiles==23.2.1

# 数据处理
pandas==2.1.3
numpy==1.24.3
openpyxl==3.1.2

# AI和HTTP客户端
openai==1.3.7
anthropic==0.7.8
httpx==0.25.2
requests==2.31.0

# 数据库
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.0

# 缓存
redis==5.0.1
hiredis==2.2.3

# 配置和环境
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0
PyYAML==6.0.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0
structlog==23.2.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 工具库
click==8.1.7
tqdm==4.66.1
python-dateutil==2.8.2
pytz==2023.3

# 安全
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# 文件处理
xlsxwriter==3.1.9
python-magic==0.4.27

# 异步任务
celery==5.3.4
kombu==5.3.4

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 部署相关
gunicorn==21.2.0
supervisor==4.2.5
