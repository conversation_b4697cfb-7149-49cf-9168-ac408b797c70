#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终大规模分析 - 每个标签500条，总计1500条
改进的不匹配原因分析
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime
import time

def analyze_mismatch_detailed(row, keyword_predicted, ai_predicted, manual_label):
    """详细的不匹配原因分析"""
    product_name = str(row['商品名']).lower()
    category = str(row['类目']).lower()
    
    # 强季节性关键词
    strong_aw_keywords = ['羽绒', '冬季', '保暖', '加厚', '防风', '毛呢', '皮草', '雪地靴', '棉拖', '冲锋衣', '毛衣', '厚外套']
    strong_ss_keywords = ['夏季', '短袖', '清凉', '防晒', '泳装', '凉鞋', '透气', '短裤', '短裙', '薄款', '凉爽']
    
    # 全季类目
    all_season_categories = ['美妆个护', '食品', '电器', '居家日用', '母婴', '宠物', '箱包', '数码', '家具', '厨具']
    
    # 检查关键词
    aw_keywords_found = [kw for kw in strong_aw_keywords if kw in product_name]
    ss_keywords_found = [kw for kw in strong_ss_keywords if kw in product_name]
    is_all_season_category = any(cat in category for cat in all_season_categories)
    
    reason = ""
    mismatch_type = ""
    confidence = "低"
    possible_error = "否"
    
    # 详细分析各种情况
    if manual_label == '全季' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含明显AW关键词{aw_keywords_found}，AI正确识别为AW，但人工误标为全季"
            mismatch_type = "明显AW商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显AW特征被误标"
        elif '外套' in product_name or '夹克' in product_name or '大衣' in product_name:
            reason = f"商品为外套类商品，通常具有季节性，AI判断为AW合理，但人工标记为全季"
            mismatch_type = "外套类商品季节性判断分歧"
            confidence = "中"
            possible_error = "可能 - 外套类商品季节性"
        else:
            reason = f"AI基于商品特征判断为AW，可能考虑了材质、厚度等因素"
            mismatch_type = "AW判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == '全季' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含明显SS关键词{ss_keywords_found}，AI正确识别为SS，但人工误标为全季"
            mismatch_type = "明显SS商品误标为全季"
            confidence = "高"
            possible_error = "是 - 明显SS特征被误标"
        elif any(kw in product_name for kw in ['连衣裙', '半身裙', '吊带', '背心']):
            reason = f"商品为裙装/轻薄服装，通常偏向夏季，AI判断为SS合理"
            mismatch_type = "夏季服装季节性判断分歧"
            confidence = "中"
            possible_error = "可能 - 夏季服装特征"
        else:
            reason = f"AI基于商品特征判断为SS，可能考虑了材质、款式等因素"
            mismatch_type = "SS判断分歧"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == 'AW' and ai_predicted == '全季':
        if is_all_season_category and not aw_keywords_found:
            reason = f"商品属于全季类目({category})且无明显AW特征，AI判断为全季更合理"
            mismatch_type = "全季商品误标为AW"
            confidence = "中"
            possible_error = "可能 - 全季类目商品"
        elif not aw_keywords_found and '鞋' in product_name:
            reason = f"鞋类商品如无明显季节特征，AI判断为全季较合理"
            mismatch_type = "鞋类商品季节性判断分歧"
            confidence = "中"
            possible_error = "可能"
        else:
            reason = f"商品可能具有AW特征，但AI判断为全季，需进一步分析"
            mismatch_type = "AW判断分歧"
            confidence = "低"
            possible_error = "否"
    
    elif manual_label == 'SS' and ai_predicted == '全季':
        if is_all_season_category and not ss_keywords_found:
            reason = f"商品属于全季类目({category})且无明显SS特征，AI判断为全季更合理"
            mismatch_type = "全季商品误标为SS"
            confidence = "中"
            possible_error = "可能 - 全季类目商品"
        elif not ss_keywords_found and '鞋' in product_name:
            reason = f"鞋类商品如无明显季节特征，AI判断为全季较合理"
            mismatch_type = "鞋类商品季节性判断分歧"
            confidence = "中"
            possible_error = "可能"
        else:
            reason = f"商品可能具有SS特征，但AI判断为全季，需进一步分析"
            mismatch_type = "SS判断分歧"
            confidence = "低"
            possible_error = "否"
    
    elif manual_label == 'AW' and ai_predicted == 'SS':
        if ss_keywords_found:
            reason = f"商品名包含SS关键词{ss_keywords_found}，AI判断为SS正确，人工标记AW错误"
            mismatch_type = "季节标签错误(AW->SS)"
            confidence = "高"
            possible_error = "是 - 明显SS特征被标为AW"
        else:
            reason = f"AI判断为SS与人工标记AW冲突，需要人工复核"
            mismatch_type = "季节判断严重分歧(AW vs SS)"
            confidence = "中"
            possible_error = "可能"
    
    elif manual_label == 'SS' and ai_predicted == 'AW':
        if aw_keywords_found:
            reason = f"商品名包含AW关键词{aw_keywords_found}，AI判断为AW正确，人工标记SS错误"
            mismatch_type = "季节标签错误(SS->AW)"
            confidence = "高"
            possible_error = "是 - 明显AW特征被标为SS"
        else:
            reason = f"AI判断为AW与人工标记SS冲突，需要人工复核"
            mismatch_type = "季节判断严重分歧(SS vs AW)"
            confidence = "中"
            possible_error = "可能"
    
    # 如果关键词预测和AI预测一致，提高置信度
    if keyword_predicted == ai_predicted and keyword_predicted != manual_label:
        if confidence == "中":
            confidence = "高"
        elif confidence == "低":
            confidence = "中"
        if possible_error == "否":
            possible_error = "可能"
    
    return reason, mismatch_type, confidence, possible_error

def main():
    """主函数"""
    print("="*80)
    print("最终大规模分析 - 分层抽样1500条")
    print("每个标签500条，总计1500条数据")
    print("="*80)
    
    try:
        # 读取数据
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")
        
        # 重命名列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })
        
        # 查看标签分布
        label_counts = df['人工标签'].value_counts()
        print(f"\n原始数据标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} 条")
        
        # 分层抽样
        sample_dfs = []
        for label in ['全季', 'SS', 'AW']:
            label_data = df[df['人工标签'] == label]
            if len(label_data) >= 500:
                label_sample = label_data.sample(n=500, random_state=42)
                sample_dfs.append(label_sample)
                print(f"  {label}: 抽样 500 条")
            else:
                sample_dfs.append(label_data)
                print(f"  {label}: 全部 {len(label_data)} 条")
        
        # 合并样本
        sample_df = pd.concat(sample_dfs, ignore_index=True)
        sample_df = sample_df.sample(frac=1, random_state=42).reset_index(drop=True)
        print(f"\n总抽样数据: {len(sample_df)} 条")
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 添加分析列
        sample_df['关键词预测'] = ''
        sample_df['AI预测'] = ''
        sample_df['匹配情况'] = ''
        sample_df['不匹配原因'] = ''
        sample_df['不匹配类型'] = ''
        sample_df['可能的标记错误'] = ''
        sample_df['置信度'] = ''
        
        print(f"\n开始处理...")
        start_time = time.time()
        
        # 批量处理
        batch_size = 20
        for i in range(0, len(sample_df), batch_size):
            batch_end = min(i + batch_size, len(sample_df))
            print(f"\n处理批次 {i//batch_size + 1}: 第 {i+1}-{batch_end} 条记录")
            
            for idx in range(i, batch_end):
                row = sample_df.iloc[idx]
                product_info = {
                    '商品ID': row['商品ID'],
                    '商品名': row['商品名'],
                    '类目': row['类目'],
                    '商品描述': row.get('商品描述', '')
                }
                
                try:
                    # 关键词分类
                    keyword_result = classifier.classify_single_product(product_info)
                    sample_df.at[sample_df.index[idx], '关键词预测'] = keyword_result
                    
                    # AI分类
                    ai_result = classifier.classify_with_ai(product_info)
                    sample_df.at[sample_df.index[idx], 'AI预测'] = ai_result
                    
                    # 分析匹配情况
                    manual_label = str(row['人工标签'])
                    
                    if manual_label == ai_result:
                        sample_df.at[sample_df.index[idx], '匹配情况'] = '匹配'
                        sample_df.at[sample_df.index[idx], '不匹配原因'] = ''
                        sample_df.at[sample_df.index[idx], '不匹配类型'] = ''
                        sample_df.at[sample_df.index[idx], '可能的标记错误'] = '否'
                        sample_df.at[sample_df.index[idx], '置信度'] = ''
                    else:
                        sample_df.at[sample_df.index[idx], '匹配情况'] = '不匹配'
                        
                        # 分析不匹配原因
                        reason, mismatch_type, confidence, possible_error = analyze_mismatch_detailed(
                            row, keyword_result, ai_result, manual_label
                        )
                        
                        sample_df.at[sample_df.index[idx], '不匹配原因'] = reason
                        sample_df.at[sample_df.index[idx], '不匹配类型'] = mismatch_type
                        sample_df.at[sample_df.index[idx], '置信度'] = confidence
                        sample_df.at[sample_df.index[idx], '可能的标记错误'] = possible_error
                    
                except Exception as e:
                    print(f"  处理失败 (第{idx+1}条): {e}")
                    sample_df.at[sample_df.index[idx], 'AI预测'] = keyword_result if 'keyword_result' in locals() else '全季'
                    sample_df.at[sample_df.index[idx], '不匹配原因'] = f"处理失败: {str(e)}"
            
            # 显示进度
            processed = min(batch_end, len(sample_df))
            elapsed_time = time.time() - start_time
            avg_time = elapsed_time / processed
            remaining_time = avg_time * (len(sample_df) - processed)
            print(f"  已处理: {processed}/{len(sample_df)} "
                  f"({processed/len(sample_df):.1%}) "
                  f"预计剩余: {remaining_time/60:.1f}分钟")
            
            # 保存中间结果
            if processed % 100 == 0 or processed == len(sample_df):
                temp_file = f"temp_results_{processed}.xlsx"
                sample_df.to_excel(temp_file, index=False)
                print(f"  中间结果已保存: {temp_file}")
        
        # 生成最终分析报告
        generate_final_report(sample_df)
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

def generate_final_report(df):
    """生成最终分析报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"\n" + "="*80)
    print("最终分析报告")
    print("="*80)
    
    # 基本统计
    total_count = len(df)
    match_count = (df['匹配情况'] == '匹配').sum()
    mismatch_count = total_count - match_count
    
    print(f"总样本数: {total_count}")
    print(f"匹配数: {match_count} ({match_count/total_count:.1%})")
    print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count:.1%})")
    
    # 各标签准确率
    print(f"\n各标签准确率:")
    accuracy_results = []
    for label in ['全季', 'SS', 'AW']:
        label_data = df[df['人工标签'] == label]
        if len(label_data) > 0:
            label_correct = (label_data['人工标签'] == label_data['AI预测']).sum()
            accuracy = label_correct / len(label_data)
            target = 0.95 if label == '全季' else 0.90
            status = "✓ 达成" if accuracy >= target else "✗ 未达成"
            print(f"  {label}: {accuracy:.1%} ({label_correct}/{len(label_data)}) 目标:{target:.0%} {status}")
            
            accuracy_results.append({
                '标签': label,
                '样本数': len(label_data),
                '正确数': label_correct,
                '准确率': f"{accuracy:.1%}",
                '目标准确率': f"{target:.0%}",
                '是否达成': status
            })
    
    # 不匹配分析
    mismatched_data = df[df['匹配情况'] == '不匹配']
    if len(mismatched_data) > 0:
        print(f"\n不匹配类型分布:")
        mismatch_types = mismatched_data['不匹配类型'].value_counts()
        for mtype, count in mismatch_types.items():
            print(f"  {mtype}: {count} 条 ({count/len(mismatched_data):.1%})")
        
        print(f"\n置信度分布:")
        confidence_dist = mismatched_data['置信度'].value_counts()
        for conf, count in confidence_dist.items():
            print(f"  {conf}: {count} 条 ({count/len(mismatched_data):.1%})")
    
    # 可能的标记错误
    possible_errors = df[df['可能的标记错误'].str.contains('是', na=False)]
    print(f"\n可能的标记错误: {len(possible_errors)} 条 ({len(possible_errors)/total_count:.1%})")
    
    # 保存所有分析文件
    print(f"\n保存分析文件...")
    
    # 1. 完整结果
    full_result_file = f"季节标签最终分析_{timestamp}.xlsx"
    df.to_excel(full_result_file, index=False)
    print(f"1. 完整分析结果: {full_result_file}")
    
    # 2. 不匹配详情表格
    if len(mismatched_data) > 0:
        mismatch_file = f"不匹配详情分析表_{timestamp}.csv"
        analysis_cols = ['商品ID', '商品名', '类目', '人工标签', 'AI预测', '关键词预测',
                        '不匹配原因', '不匹配类型', '置信度', '可能的标记错误']
        mismatched_data[analysis_cols].to_csv(mismatch_file, index=False, encoding='utf-8-sig')
        print(f"2. 不匹配详情分析表: {mismatch_file}")
    
    # 3. 可能的标记错误表格
    if len(possible_errors) > 0:
        error_file = f"可能的标记错误表_{timestamp}.csv"
        possible_errors[analysis_cols].to_csv(error_file, index=False, encoding='utf-8-sig')
        print(f"3. 可能的标记错误表: {error_file}")
    
    # 4. 准确率汇总表格
    accuracy_df = pd.DataFrame(accuracy_results)
    accuracy_file = f"准确率汇总表_{timestamp}.csv"
    accuracy_df.to_csv(accuracy_file, index=False, encoding='utf-8-sig')
    print(f"4. 准确率汇总表: {accuracy_file}")
    
    # 5. 不匹配类型汇总表格
    if len(mismatched_data) > 0:
        mismatch_summary = mismatched_data.groupby(['不匹配类型', '置信度']).size().reset_index(name='数量')
        summary_file = f"不匹配类型汇总表_{timestamp}.csv"
        mismatch_summary.to_csv(summary_file, index=False, encoding='utf-8-sig')
        print(f"5. 不匹配类型汇总表: {summary_file}")
    
    print(f"\n" + "="*80)
    print("分析完成！")
    print("="*80)
    print(f"主要结论:")
    print(f"1. 总体准确率: {match_count/total_count:.1%}")
    print(f"2. 可能的原始数据标记错误: {len(possible_errors)} 条")
    print(f"3. 建议优先修正'置信度'为'高'的标记错误")
    print(f"4. 系统在识别明显季节性特征方面表现良好")

if __name__ == "__main__":
    main()
