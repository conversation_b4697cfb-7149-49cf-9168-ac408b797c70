# 季节标签分类系统 - 使用指南

## 系统概述

本系统基于大模型和关键词匹配，实现商品季节标签的自动分类，支持三种标签：
- **全季**：适用于一年四季的商品
- **SS（春夏）**：主要在春夏季节使用的商品  
- **AW（秋冬）**：主要在秋冬季节使用的商品

## 快速开始

### 1. 环境准备

确保已安装必要的Python包：
```bash
pip install pandas openpyxl openai numpy
```

### 2. 数据准备

将商品数据保存为Excel文件，命名为`上架商品季节标签.xlsx`，包含以下列：
- `item_id`：商品ID
- `商品名`：商品名称
- `物理一级类目`：商品类目
- `季节标签`：人工标注的季节标签（用于对比）

### 3. 运行分析

#### 方式一：使用主程序（推荐）
```bash
python run_season_classification.py
```

选择运行模式：
- **1. 测试模式**：验证系统功能（5个内置测试用例）
- **2. 样本模式**：处理50条样本数据
- **3. 完整模式**：处理全部数据
- **4. 检查模式**：查看Excel文件结构

#### 方式二：快速分析
```bash
python quick_analysis.py
```
每个标签抽样50条，总计150条数据，快速验证系统效果。

#### 方式三：大规模分析
```bash
python final_large_analysis.py
```
每个标签抽样500条，总计1500条数据，进行详细分析。

## 输出文件说明

### 1. 完整分析结果
- **文件名**：`季节标签分析结果_YYYYMMDD_HHMMSS.xlsx`
- **内容**：包含所有商品的完整分析结果
- **字段**：
  - 原始商品信息（ID、名称、类目等）
  - 关键词预测标签
  - AI预测标签
  - 匹配情况
  - 不匹配原因分析

### 2. 不匹配详情分析表
- **文件名**：`不匹配详情分析_YYYYMMDD_HHMMSS.csv`
- **内容**：所有不匹配商品的详细分析
- **用途**：重点分析预测错误的原因

### 3. 可能的标记错误表
- **文件名**：`可能的标记错误_YYYYMMDD_HHMMSS.csv`
- **内容**：系统认为原始数据可能标记错误的商品
- **用途**：发现和修正数据质量问题

### 4. 准确率汇总表
- **文件名**：`准确率汇总_YYYYMMDD_HHMMSS.csv`
- **内容**：各标签的准确率统计
- **用途**：评估系统整体性能

### 5. 不匹配类型汇总表
- **文件名**：`不匹配类型汇总_YYYYMMDD_HHMMSS.csv`
- **内容**：不匹配模式的统计分析
- **用途**：了解系统的主要错误类型

## 分析结果解读

### 1. 准确率指标
- **目标准确率**：全季95%，SS90%，AW90%
- **实际表现**：根据测试结果调整期望
- **改进方向**：重点关注未达标的标签

### 2. 不匹配类型
- **明显季节性商品误标为全季**：置信度高，建议修正
- **全季商品误标为季节性**：需要人工复核
- **季节判断分歧**：边界模糊商品，需要制定标准
- **季节标签错误**：AW和SS之间的错误，优先修正

### 3. 置信度等级
- **高**：系统非常确信，建议优先处理
- **中**：需要人工判断
- **低**：可能是边界情况，暂时保留

## 系统优化建议

### 1. 关键词库优化
编辑`season_label_classifier.py`中的关键词列表：

```python
# AW关键词
self.aw_keywords = [
    '羽绒', '冬', '保暖', '加厚', '防风', '毛呢', 
    '皮草', '羊毛', '冲锋衣', '雪地靴', '秋', 
    '厚', '抗寒', '棉拖', '靴子'
    # 添加新的AW关键词
]

# SS关键词  
self.ss_keywords = [
    '夏', '短袖', '清凉', '防晒', '泳装', 
    '凉鞋', '透气'
    # 添加新的SS关键词
]
```

### 2. 提示词调整
修改`prompts/prompt`文件，优化AI判断逻辑：

```
基于以下逻辑对商品的季节标签进行判断：
步骤1: 检查类目信息（物理类目优先）
步骤2: 检查商品名和商品可能使用的季节
步骤3: 检查商品描述（补充作用）

季节标签只能是：全季、SS、AW

[在此添加具体的判断规则]
```

### 3. API配置
修改API密钥和地址：

```python
API_KEY = "your_api_key"
BASE_URL = "your_base_url"
```

## 常见问题解决

### 1. Excel文件读取失败
- 检查文件路径和格式
- 确保文件未被其他程序占用
- 验证列名是否正确

### 2. API调用失败
- 检查网络连接
- 验证API密钥和URL
- 确认API服务可用性

### 3. 内存不足
- 减少批量处理大小
- 使用样本模式而非完整模式
- 分批处理大文件

### 4. 处理速度慢
- 调整批量大小（默认20）
- 检查网络延迟
- 考虑使用更快的API服务

## 高级功能

### 1. 自定义分析
创建自己的分析脚本：

```python
from season_label_classifier import SeasonLabelClassifier

# 初始化分类器
classifier = SeasonLabelClassifier(API_KEY, BASE_URL)

# 分类单个商品
product_info = {
    '商品名': '商品名称',
    '类目': '商品类目',
    '商品描述': '商品描述'
}

result = classifier.classify_with_ai(product_info)
print(f"预测标签: {result}")
```

### 2. 批量处理自定义数据
```python
import pandas as pd

# 读取自定义数据
df = pd.read_excel("your_data.xlsx")

# 处理数据
result_df = classifier.process_excel_file("your_data.xlsx")

# 保存结果
result_df.to_excel("results.xlsx", index=False)
```

### 3. 结果后处理
```python
# 筛选高置信度的错误
high_confidence_errors = df[
    (df['匹配情况'] == '不匹配') & 
    (df['置信度'] == '高')
]

# 统计各类目的准确率
category_accuracy = df.groupby('类目').apply(
    lambda x: (x['人工标签'] == x['AI预测']).mean()
)
```

## 技术支持

### 1. 日志查看
系统运行时会输出详细的处理日志，包括：
- 处理进度
- 错误信息
- 中间结果保存位置

### 2. 调试模式
运行测试脚本进行调试：
```bash
python test_classifier.py
```

### 3. 性能监控
- 关注API调用频率
- 监控内存使用情况
- 记录处理时间

## 最佳实践

### 1. 数据质量
- 确保商品名称完整准确
- 类目信息尽可能详细
- 人工标签标准一致

### 2. 分批处理
- 大数据集分批处理
- 定期保存中间结果
- 设置合理的批量大小

### 3. 结果验证
- 重点检查高置信度的不匹配项
- 定期更新关键词库
- 根据业务需求调整判断标准

### 4. 持续改进
- 收集用户反馈
- 分析错误模式
- 定期优化算法参数

---

如有其他问题，请参考`项目总结报告.md`或联系技术支持团队。
