#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的季节标签分类测试
"""

import pandas as pd
from season_label_classifier import SeasonLabelClassifier
from datetime import datetime

def main():
    """主函数"""
    print("开始简化测试...")
    
    # 读取Excel文件
    try:
        df = pd.read_excel("上架商品季节标签.xlsx")
        print(f"成功读取Excel文件，共{len(df)}条记录")
        print(f"列名: {list(df.columns)}")
        
        # 重命名关键列
        df = df.rename(columns={
            'item_id': '商品ID',
            '季节标签': '人工标签',
            '物理一级类目': '类目'
        })
        
        # 取前10条数据进行测试
        test_df = df.head(10).copy()
        print(f"\n测试前10条数据:")
        print(test_df[['商品ID', '商品名', '类目', '人工标签']].to_string(index=False))
        
        # 初始化分类器
        API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        classifier = SeasonLabelClassifier(API_KEY, BASE_URL)
        
        # 添加预测列
        test_df['关键词预测'] = ''
        test_df['AI预测'] = ''
        test_df['匹配情况'] = ''
        
        print(f"\n开始分类...")
        
        # 逐个处理
        for idx, row in test_df.iterrows():
            product_info = {
                '商品ID': row['商品ID'],
                '商品名': row['商品名'],
                '类目': row['类目'],
                '商品描述': row.get('商品描述', '')
            }
            
            # 关键词分类
            keyword_result = classifier.classify_single_product(product_info)
            test_df.at[idx, '关键词预测'] = keyword_result
            
            # AI分类
            try:
                ai_result = classifier.classify_with_ai(product_info)
                test_df.at[idx, 'AI预测'] = ai_result
            except Exception as e:
                print(f"AI分类失败 (商品ID: {row['商品ID']}): {e}")
                test_df.at[idx, 'AI预测'] = keyword_result
            
            # 检查匹配
            manual_label = str(row['人工标签'])
            ai_predicted = str(test_df.at[idx, 'AI预测'])
            test_df.at[idx, '匹配情况'] = '匹配' if manual_label == ai_predicted else '不匹配'
            
            print(f"商品ID {row['商品ID']}: {row['商品名'][:20]}... -> 人工:{manual_label}, AI:{ai_predicted}")
        
        # 显示结果
        print(f"\n" + "="*80)
        print("分类结果:")
        print("="*80)
        
        result_cols = ['商品ID', '商品名', '人工标签', '关键词预测', 'AI预测', '匹配情况']
        print(test_df[result_cols].to_string(index=False))
        
        # 计算准确率
        valid_data = test_df[test_df['人工标签'].notna() & test_df['AI预测'].notna()]
        if len(valid_data) > 0:
            total_correct = (valid_data['人工标签'].astype(str) == valid_data['AI预测'].astype(str)).sum()
            accuracy = total_correct / len(valid_data)
            print(f"\n总体准确率: {accuracy:.2%} ({total_correct}/{len(valid_data)})")
            
            # 各标签准确率
            for label in ['全季', 'SS', 'AW']:
                label_data = valid_data[valid_data['人工标签'].astype(str) == label]
                if len(label_data) > 0:
                    label_correct = (label_data['人工标签'].astype(str) == label_data['AI预测'].astype(str)).sum()
                    label_accuracy = label_correct / len(label_data)
                    print(f"{label}准确率: {label_accuracy:.2%} ({label_correct}/{len(label_data)})")
        
        # 保存结果
        output_file = f"简化测试结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        test_df.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
